<div>
    <!-- Header da seção -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-zinc-700 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                        {{ $isOwner ? 'Meus Reels' : 'Reels de ' . $user->name }}
                    </h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        {{ count($reels) }} {{ count($reels) === 1 ? 'reel' : 'reels' }}
                    </p>
                </div>
            </div>
            
            @if($isOwner)
                <livewire:stories.create-reel />
            @endif
        </div>

        <!-- Grid de reels -->
        @if(count($reels) > 0)
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                @foreach($reels as $index => $reel)
                    <div class="relative group cursor-pointer" wire:click="openReelViewer({{ $index }})">
                        <!-- Thumbnail do vídeo -->
                        <div class="aspect-[9/16] bg-gray-200 dark:bg-zinc-700 rounded-lg overflow-hidden">
                            <video 
                                class="w-full h-full object-cover"
                                muted
                                preload="metadata"
                            >
                                <source src="{{ $reel['video'] }}#t=1" type="video/mp4">
                            </video>
                            
                            <!-- Overlay com informações -->
                            <div class="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors">
                                <!-- Ícone de play -->
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white/30 transition-colors">
                                        <svg class="w-6 h-6 text-white ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M9 10v4a2 2 0 002 2h2a2 2 0 002-2v-4M9 10V9a2 2 0 012-2h2a2 2 0 012 2v1"></path></svg>
                                    </div>
                                </div>
                                
                                <!-- Informações na parte inferior -->
                                <div class="absolute bottom-0 left-0 right-0 p-3">
                                    <!-- Curtidas -->
                                    <div class="flex items-center space-x-1 text-white text-sm">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>
                                        <span>{{ $reel['likes_count'] }}</span>
                                    </div>
                                    
                                    <!-- Música (se houver) -->
                                    @if($reel['music_title'])
                                        <div class="flex items-center space-x-1 text-white text-xs mt-1">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>
                                            <span class="truncate">{{ $reel['music_title'] }}</span>
                                        </div>
                                    @endif
                                </div>
                                
                                <!-- Botão de deletar (apenas para o dono) -->
                                @if($isOwner)
                                    <button 
                                        wire:click.stop="deleteReel({{ $reel['id'] }})"
                                        class="absolute top-2 right-2 w-8 h-8 bg-red-500/80 hover:bg-red-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                        onclick="return confirm('Tem certeza que deseja deletar este reel?')"
                                    >
                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                                    </button>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Data de criação -->
                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                            {{ $reel['created_at'] }}
                        </p>
                    </div>
                @endforeach
            </div>
        @else
            <!-- Estado vazio -->
            <div class="text-center py-12">
                <div class="w-20 h-20 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-10 h-10 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>
                </div>
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {{ $isOwner ? 'Você ainda não criou nenhum reel' : 'Nenhum reel encontrado' }}
                </h4>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    {{ $isOwner ? 'Crie seu primeiro reel e compartilhe momentos incríveis!' : 'Este usuário ainda não publicou reels.' }}
                </p>
                @if($isOwner)
                    <livewire:stories.create-reel />
                @endif
            </div>
        @endif
    </div>

    <!-- Modal do visualizador de reels -->
    @if($showModal && count($reels) > 0)
        <div class="fixed inset-0 bg-black z-50 flex items-center justify-center">
            <!-- Reel atual -->
            <div class="relative w-full max-w-md h-full flex flex-col">
                <!-- Header -->
                <div class="flex items-center justify-between p-4 text-white">
                    <div class="flex items-center space-x-3">
                        <img 
                            src="{{ $user->userPhotos->first() ? Storage::url($user->userPhotos->first()->photo_path) : asset('images/default-avatar.svg') }}" 
                            alt="{{ $user->name }}"
                            class="w-8 h-8 rounded-full"
                        >
                        <div>
                            <p class="font-semibold">{{ $user->username }}</p>
                            <p class="text-xs text-gray-300">{{ $reels[$currentReelIndex]['created_at'] }}</p>
                        </div>
                    </div>
                    
                    <button wire:click="closeReelViewer" class="text-white hover:text-gray-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                </div>

                <!-- Vídeo -->
                <div class="flex-1 relative">
                    <video 
                        class="w-full h-full object-contain"
                        controls
                        autoplay
                        loop
                        key="{{ $reels[$currentReelIndex]['id'] }}"
                    >
                        <source src="{{ $reels[$currentReelIndex]['video'] }}" type="video/mp4">
                    </video>
                </div>

                <!-- Informações do reel -->
                <div class="p-4 text-white">
                    @if($reels[$currentReelIndex]['content'])
                        <p class="mb-2">{{ $reels[$currentReelIndex]['content'] }}</p>
                    @endif
                    
                    @if($reels[$currentReelIndex]['music_title'])
                        <div class="flex items-center space-x-2 text-sm text-gray-300">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>
                            <span>{{ $reels[$currentReelIndex]['music_title'] }}</span>
                        </div>
                    @endif
                    
                    <div class="flex items-center space-x-4 mt-3 text-sm">
                        <div class="flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>
                            <span>{{ $reels[$currentReelIndex]['likes_count'] }} curtidas</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>
                            <span>{{ $reels[$currentReelIndex]['comments_count'] }} comentários</span>
                        </div>
                    </div>
                </div>

                <!-- Navegação -->
                @if(count($reels) > 1)
                    <div class="absolute inset-y-0 left-0 w-1/3 flex items-center justify-start">
                        <button 
                            wire:click="previousReel" 
                            class="text-white/50 hover:text-white p-4"
                            @if($currentReelIndex === 0) disabled @endif
                        >
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
                        </button>
                    </div>
                    
                    <div class="absolute inset-y-0 right-0 w-1/3 flex items-center justify-end">
                        <button 
                            wire:click="nextReel" 
                            class="text-white/50 hover:text-white p-4"
                            @if($currentReelIndex === count($reels) - 1) disabled @endif
                        >
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
                        </button>
                    </div>
                @endif

                <!-- Indicador de posição -->
                @if(count($reels) > 1)
                    <div class="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex space-x-1">
                        @foreach($reels as $index => $reel)
                            <div class="w-2 h-2 rounded-full {{ $index === $currentReelIndex ? 'bg-white' : 'bg-white/30' }}"></div>
                        @endforeach
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>
