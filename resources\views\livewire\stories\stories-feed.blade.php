<div>
    <!-- Lista horizontal de stories -->
    <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-zinc-700 p-4 mb-6">
        <div class="stories-scroll flex items-center space-x-4 overflow-x-auto" style="-ms-overflow-style: none; scrollbar-width: none;">
            <style>
                .stories-scroll::-webkit-scrollbar {
                    display: none;
                }
            </style>
            @if(count($stories) > 0)
                @foreach($stories as $index => $userStory)
                    <div class="flex-shrink-0">
                        <button 
                            wire:click="viewStory({{ $userStory['user']['id'] }})"
                            class="flex flex-col items-center space-y-2 group"
                        >
                            <!-- Avatar com indicador de story -->
                            <div class="relative">
                                <div class="w-16 h-16 rounded-full p-1 
                                    {{ $userStory['has_unviewed'] 
                                        ? 'bg-gradient-to-tr from-purple-500 via-pink-500 to-orange-500' 
                                        : 'bg-gray-300' 
                                    }}">
                                    <img 
                                        src="{{ $userStory['user']['avatar'] }}" 
                                        alt="{{ $userStory['user']['name'] }}"
                                        class="w-full h-full rounded-full object-cover border-2 border-white"
                                    >
                                </div>
                                
                                <!-- Indicador de quantidade de stories -->
                                @if($userStory['total_stories'] > 1)
                                    <div class="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                        {{ $userStory['total_stories'] }}
                                    </div>
                                @endif
                            </div>
                            
                            <!-- Nome do usuário -->
                            <span class="text-xs text-gray-700 dark:text-gray-300 max-w-16 truncate">
                                {{ $userStory['user']['username'] }}
                            </span>
                        </button>
                    </div>
                @endforeach
            @else
                <div class="flex items-center justify-center w-full py-8">
                    <div class="text-center">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>
                        <p class="text-gray-500 dark:text-gray-400">Nenhum story disponível</p>
                        <p class="text-sm text-gray-400 dark:text-gray-500">Seja o primeiro a compartilhar um momento!</p>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Visualizador de stories -->
    @if($showViewer && $currentStory)
        <div class="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center">
            <div class="relative w-full max-w-md h-full flex flex-col">
                <!-- Header do story -->
                <div class="flex items-center justify-between p-4 text-white">
                    <div class="flex items-center space-x-3">
                        <img 
                            src="{{ $currentStory['user']['avatar'] }}" 
                            alt="{{ $currentStory['user']['name'] }}"
                            class="w-8 h-8 rounded-full"
                        >
                        <div>
                            <p class="font-semibold">{{ $currentStory['user']['username'] }}</p>
                            <p class="text-xs text-gray-300">
                                {{ $currentStory['stories'][0]['created_at'] ?? '' }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <!-- Botão de deletar (apenas para próprios stories) -->
                        @if($currentStory['user']['id'] == Auth::id())
                            <button 
                                wire:click="deleteStory({{ $currentStory['stories'][0]['id'] ?? 0 }})"
                                class="text-red-400 hover:text-red-300"
                                onclick="return confirm('Tem certeza que deseja deletar este story?')"
                            >
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                            </button>
                        @endif
                        
                        <!-- Botão de fechar -->
                        <button wire:click="closeViewer" class="text-white hover:text-gray-300">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                        </button>
                    </div>
                </div>

                <!-- Barra de progresso -->
                <div class="flex space-x-1 px-4 mb-4">
                    @foreach($currentStory['stories'] as $index => $story)
                        <div class="flex-1 h-1 bg-gray-600 rounded-full overflow-hidden">
                            <div class="h-full bg-white rounded-full w-full"></div>
                        </div>
                    @endforeach
                </div>

                <!-- Conteúdo do story -->
                <div class="flex-1 flex items-center justify-center px-4">
                    @if($currentStory['stories'][0]['image'] ?? false)
                        <img 
                            src="{{ $currentStory['stories'][0]['image'] }}" 
                            alt="Story"
                            class="max-w-full max-h-full object-contain rounded-lg"
                        >
                    @elseif($currentStory['stories'][0]['video'] ?? false)
                        <video 
                            controls 
                            autoplay 
                            class="max-w-full max-h-full object-contain rounded-lg"
                        >
                            <source src="{{ $currentStory['stories'][0]['video'] }}" type="video/mp4">
                        </video>
                    @endif
                </div>

                <!-- Texto do story -->
                @if($currentStory['stories'][0]['content'] ?? false)
                    <div class="p-4 text-white text-center">
                        <p class="text-lg">{{ $currentStory['stories'][0]['content'] }}</p>
                    </div>
                @endif

                <!-- Música (se houver) -->
                @if($currentStory['stories'][0]['music_title'] ?? false)
                    <div class="p-4 text-white text-center">
                        <div class="flex items-center justify-center space-x-2">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>
                            <span class="text-sm">{{ $currentStory['stories'][0]['music_title'] }}</span>
                        </div>
                    </div>
                @endif

                <!-- Controles de navegação -->
                <div class="absolute inset-y-0 left-0 w-1/3 flex items-center justify-start">
                    <button 
                        wire:click="previousStory" 
                        class="text-white hover:text-gray-300 p-4"
                    >
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg>
                    </button>
                </div>
                
                <div class="absolute inset-y-0 right-0 w-1/3 flex items-center justify-end">
                    <button 
                        wire:click="nextStory" 
                        class="text-white hover:text-gray-300 p-4"
                    >
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>
                    </button>
                </div>

                <!-- Informações do story (visualizações para próprios stories) -->
                @if($currentStory['user']['id'] == Auth::id())
                    <div class="p-4 text-white text-center">
                        <div class="flex items-center justify-center space-x-4 text-sm">
                            <div class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path></svg>
                                <span>{{ $currentStory['stories'][0]['views_count'] ?? 0 }} visualizações</span>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif
</div>
