<div>
    <!-- Stories em Destaque -->
    @if(count($highlights) > 0)
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-zinc-700 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center">
                        <flux:icon.star  />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                            {{ $isOwner ? 'Meus Destaques' : 'Destaques de ' . $user->name }}
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ count($highlights) }} {{ count($highlights) === 1 ? 'destaque' : 'destaques' }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Grid de destaques -->
            <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3">
                @foreach($highlights as $highlight)
                    <div class="relative group">
                        <!-- Círculo do destaque -->
                        <div class="aspect-square bg-gradient-to-tr from-yellow-500 via-orange-500 to-red-500 rounded-full p-1">
                            <div class="w-full h-full bg-white dark:bg-zinc-800 rounded-full p-1">
                                @if($highlight['image'])
                                    <img 
                                        src="{{ $highlight['image'] }}" 
                                        alt="Destaque"
                                        class="w-full h-full rounded-full object-cover"
                                    >
                                @elseif($highlight['video'])
                                    <video 
                                        class="w-full h-full rounded-full object-cover"
                                        muted
                                        preload="metadata"
                                    >
                                        <source src="{{ $highlight['video'] }}#t=1" type="video/mp4">
                                    </video>
                                @else
                                    <div class="w-full h-full rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                                        <flux:icon.camera name="camera" class="w-6 h-6 text-white" />
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Overlay com controles (apenas para o dono) -->
                        @if($isOwner)
                            <div class="absolute inset-0 bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                                <button 
                                    wire:click="removeFromHighlights({{ $highlight['id'] }})"
                                    class="w-8 h-8 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center"
                                    title="Remover dos destaques"
                                >
                                    <x-flux:icon name="x-mark" class="w-4 h-4 text-white" />
                                </button>
                            </div>
                        @endif

                        <!-- Título do destaque -->
                        <p class="text-xs text-gray-600 dark:text-gray-400 mt-2 text-center truncate">
                            {{ $highlight['content'] ?: 'Destaque' }}
                        </p>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <!-- Stories Ativos -->
    @if(count($stories) > 0)
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-zinc-700 p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                        <flux:icon.camera class="w-6 h-6 text-white" />
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                            {{ $isOwner ? 'Meus Stories Ativos' : 'Stories de ' . $user->name }}
                        </h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            {{ count($stories) }} {{ count($stories) === 1 ? 'story ativo' : 'stories ativos' }}
                        </p>
                    </div>
                </div>
                
                @if($isOwner)
                    <livewire:stories.create-story />
                @endif
            </div>

            <!-- Grid de stories -->
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3">
                @foreach($stories as $story)
                    <div class="relative group">
                        <!-- Container do story -->
                        <div class="aspect-[9/16] bg-gray-200 dark:bg-zinc-700 rounded-lg overflow-hidden">
                            @if($story['image'])
                                <img 
                                    src="{{ $story['image'] }}" 
                                    alt="Story"
                                    class="w-full h-full object-cover"
                                >
                            @elseif($story['video'])
                                <video 
                                    class="w-full h-full object-cover"
                                    muted
                                    preload="metadata"
                                >
                                    <source src="{{ $story['video'] }}#t=1" type="video/mp4">
                                </video>
                            @else
                                <div class="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                                    <div class="text-center text-white p-4">
                                        <flux:icon.chat-bubble-left class="w-8 h-8 mx-auto mb-2" />
                                        <p class="text-xs">{{ Str::limit($story['content'], 50) }}</p>
                                    </div>
                                </div>
                            @endif
                            
                            <!-- Overlay com informações -->
                            <div class="absolute inset-0 bg-black/20 group-hover:bg-black/40 transition-colors">
                                <!-- Informações na parte inferior -->
                                <div class="absolute bottom-0 left-0 right-0 p-3">
                                    <!-- Visualizações (apenas para o dono) -->
                                    @if($isOwner)
                                        <div class="flex items-center space-x-1 text-white text-sm mb-1">
                                            <flux:icon.eye class="w-4 h-4" />
                                            <span>{{ $story['views_count'] }}</span>
                                        </div>
                                    @endif
                                    
                                    <!-- Música (se houver) -->
                                    @if($story['music_title'])
                                        <div class="flex items-center space-x-1 text-white text-xs">
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>
                                            <span class="truncate">{{ $story['music_title'] }}</span>
                                        </div>
                                    @endif
                                </div>
                                
                                <!-- Controles (apenas para o dono) -->
                                @if($isOwner)
                                    <div class="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                        <!-- Adicionar aos destaques -->
                                        @if(!$story['is_highlight'])
                                            <button 
                                                wire:click="addToHighlights({{ $story['id'] }})"
                                                class="w-8 h-8 bg-yellow-500/80 hover:bg-yellow-500 rounded-full flex items-center justify-center"
                                                title="Adicionar aos destaques"
                                            >
                                                <x-flux:icon name="star" class="w-4 h-4 text-white" />
                                            </button>
                                        @endif
                                        
                                        <!-- Deletar story -->
                                        <button 
                                            wire:click="deleteStory({{ $story['id'] }})"
                                            class="w-8 h-8 bg-red-500/80 hover:bg-red-500 rounded-full flex items-center justify-center"
                                            onclick="return confirm('Tem certeza que deseja deletar este story?')"
                                            title="Deletar story"
                                        >
                                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        <!-- Tempo restante -->
                        <div class="mt-2 text-center">
                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $story['created_at'] }}
                            </p>
                            @if($story['expires_at'])
                                <p class="text-xs text-orange-500 dark:text-orange-400">
                                    Expira {{ \Carbon\Carbon::parse($story['expires_at'])->diffForHumans() }}
                                </p>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    @elseif($isOwner)
        <!-- Estado vazio para o dono -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-zinc-700 p-6 mb-6">
            <div class="text-center py-12">
                <div class="w-20 h-20 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-4">
                    <flux:icon.camera class="w-10 h-10 text-gray-400 dark:text-gray-500" />
                </div>
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Você ainda não criou nenhum story
                </h4>
                <p class="text-gray-600 dark:text-gray-400 mb-4">
                    Compartilhe momentos especiais que desaparecem em 24 horas!
                </p>
                <livewire:stories.create-story />
            </div>
        </div>
    @endif
</div>
