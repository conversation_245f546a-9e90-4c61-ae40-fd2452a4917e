@import 'tailwindcss';
@import '../../vendor/livewire/flux/dist/flux.css';
@import './text-colors.css';
@import './night-board.css';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --font-sans: 'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --color-zinc-50: #fafafa;
    --color-zinc-100: #f5f5f5;
    --color-zinc-200: #e5e5e5;
    --color-zinc-300: #d4d4d4;
    --color-zinc-400: #a3a3a3;
    --color-zinc-500: #737373;
    --color-zinc-600: #525252;
    --color-zinc-700: #404040;
    --color-zinc-800: #262626;
    --color-zinc-900: #171717;
    --color-zinc-950: #0a0a0a;

    --color-accent: var(--color-neutral-800);
    --color-accent-content: var(--color-neutral-800);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-white);
        --color-accent-content: var(--color-white);
        --color-accent-foreground: var(--color-neutral-800);
    }
}

@layer base {

    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

[data-flux-field] {
    @apply grid gap-2;
}

[data-flux-label] {
    @apply !mb-0 !leading-tight;
}

input:focus[data-flux-control],
textarea:focus[data-flux-control],
select:focus[data-flux-control] {
    @apply outline-hidden ring-2 ring-accent ring-offset-2 ring-offset-accent-foreground;
}

/* \[:where(&)\]:size-4 {
    @apply size-4;
} */

/* Customização de Tooltips - Fundo preto e letras brancas maiores */
@layer components {

    /* Tooltip do Flux UI - Customização */
    [data-flux-tooltip-content] {
        @apply !bg-black !text-white !text-sm !py-3 !px-4 !font-medium;
        @apply !border !border-gray-600 !shadow-xl;
        @apply !rounded-lg;
        min-width: 120px !important;
        max-width: 300px !important;
        line-height: 1.4 !important;
        z-index: 9999 !important;
    }

    /* Tooltip customizado para textos longos */
    [data-flux-tooltip-content].tooltip-large {
        @apply !text-base !py-4 !px-5;
        min-width: 200px !important;
        max-width: 400px !important;
    }

    /* Tooltip para elementos ui-tooltip */
    ui-tooltip [popover] {
        @apply !bg-black !text-white !text-sm !py-3 !px-4 !font-medium;
        @apply !border !border-gray-600 !shadow-xl;
        @apply !rounded-lg;
        min-width: 120px !important;
        max-width: 300px !important;
        line-height: 1.4 !important;
        z-index: 9999 !important;
    }

    /* Tooltip para elementos ui-dropdown quando usado como tooltip */
    ui-dropdown[data-flux-tooltip] [popover] {
        @apply !bg-black !text-white !text-sm !py-3 !px-4 !font-medium;
        @apply !border !border-gray-600 !shadow-xl;
        @apply !rounded-lg;
        min-width: 120px !important;
        max-width: 300px !important;
        line-height: 1.4 !important;
        z-index: 9999 !important;
    }

    /* Garantir que o texto seja sempre branco */
    [data-flux-tooltip-content] *,
    ui-tooltip [popover] *,
    ui-dropdown[data-flux-tooltip] [popover] * {
        @apply !text-white;
    }

    /* Estilo para kbd dentro do tooltip */
    [data-flux-tooltip-content] .text-zinc-300,
    ui-tooltip [popover] .text-zinc-300,
    ui-dropdown[data-flux-tooltip] [popover] .text-zinc-300 {
        @apply !text-gray-300;
    }

    /* Scrollbar hide utility classes */
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
}