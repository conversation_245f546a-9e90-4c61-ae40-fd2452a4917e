<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            // Campos para Stories/Reels
            $table->enum('type', ['post', 'story', 'reel'])->default('post')->after('video');
            $table->timestamp('expires_at')->nullable()->after('type'); // Para stories temporários
            $table->json('story_views')->nullable()->after('expires_at'); // IDs dos usuários que visualizaram
            $table->boolean('is_highlight')->default(false)->after('story_views'); // Stories em destaque
            $table->string('music_url')->nullable()->after('is_highlight'); // URL da música para reels
            $table->string('music_title')->nullable()->after('music_url'); // Título da música
            $table->json('filters')->nullable()->after('music_title'); // Filtros aplicados
            $table->integer('duration')->nullable()->after('filters'); // Duração em segundos
            
            // Índices para performance
            $table->index(['type', 'created_at']);
            $table->index(['user_id', 'type']);
            $table->index(['expires_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('posts', function (Blueprint $table) {
            // Remover índices primeiro
            $table->dropIndex(['type', 'created_at']);
            $table->dropIndex(['user_id', 'type']);
            $table->dropIndex(['expires_at']);
            
            // Remover colunas
            $table->dropColumn([
                'type',
                'expires_at',
                'story_views',
                'is_highlight',
                'music_url',
                'music_title',
                'filters',
                'duration'
            ]);
        });
    }
};
