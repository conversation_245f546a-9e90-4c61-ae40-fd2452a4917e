<div class="max-w-md mx-auto bg-black min-h-screen">
    @if(count($reels) > 0)
        @foreach($reels as $index => $reel)
            <div class="relative h-screen flex flex-col {{ $index === $currentReelIndex ? 'block' : 'hidden' }}">
                <!-- Vídeo do reel -->
                <div class="flex-1 relative">
                    <video 
                        class="w-full h-full object-cover"
                        autoplay
                        loop
                        muted
                        playsinline
                        id="reel-video-{{ $reel['id'] }}"
                    >
                        <source src="{{ $reel['video'] }}" type="video/mp4">
                    </video>

                    <!-- Overlay com informações -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent">
                        <!-- Header com informações do usuário -->
                        <div class="absolute top-4 left-4 right-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <img 
                                    src="{{ $reel['user']['avatar'] }}" 
                                    alt="{{ $reel['user']['name'] }}"
                                    class="w-10 h-10 rounded-full border-2 border-white"
                                >
                                <div>
                                    <p class="text-white font-semibold">{{ $reel['user']['username'] }}</p>
                                    <p class="text-white/80 text-sm">{{ $reel['created_at'] }}</p>
                                </div>
                            </div>
                            
                            <!-- Botão seguir (se não for próprio reel) -->
                            @if($reel['user']['id'] != Auth::id())
                                <button class="px-3 py-1 text-sm font-medium bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30 rounded-md transition-colors">
                                    Seguir
                                </button>
                            @endif
                        </div>

                        <!-- Controles laterais -->
                        <div class="absolute right-4 bottom-20 flex flex-col space-y-4">
                            <!-- Curtir -->
                            <button 
                                wire:click="toggleLike({{ $reel['id'] }})"
                                class="flex flex-col items-center space-y-1 group"
                            >
                                <div class="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-colors">
                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg> 
                                        class="w-6 h-6 {{ $reel['is_liked'] ? 'text-red-500 fill-current' : 'text-white' }}"
                                    />
                                </div>
                                <span class="text-white text-xs">{{ $reel['likes_count'] }}</span>
                            </button>

                            <!-- Comentar -->
                            <button 
                                wire:click="toggleComments({{ $reel['id'] }})"
                                class="flex flex-col items-center space-y-1 group"
                            >
                                <div class="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-colors">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>
                                </div>
                                <span class="text-white text-xs">{{ $reel['comments_count'] }}</span>
                            </button>

                            <!-- Compartilhar -->
                            <button class="flex flex-col items-center space-y-1 group">
                                <div class="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center group-hover:bg-white/30 transition-colors">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path></svg>
                                </div>
                            </button>

                            <!-- Música (se houver) -->
                            @if($reel['music_title'])
                                <div class="flex flex-col items-center space-y-1">
                                    <div class="w-12 h-12 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center animate-spin">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Informações na parte inferior -->
                        <div class="absolute bottom-4 left-4 right-20">
                            <!-- Descrição -->
                            @if($reel['content'])
                                <p class="text-white mb-2 text-sm leading-relaxed">
                                    {{ $reel['content'] }}
                                </p>
                            @endif

                            <!-- Música -->
                            @if($reel['music_title'])
                                <div class="flex items-center space-x-2 mb-2">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>
                                    <span class="text-white text-sm">{{ $reel['music_title'] }}</span>
                                </div>
                            @endif
                        </div>

                        <!-- Navegação entre reels -->
                        <div class="absolute inset-y-0 left-0 w-1/3 flex items-center justify-start">
                            <button 
                                wire:click="previousReel" 
                                class="text-white/50 hover:text-white p-4"
                                @if($currentReelIndex === 0) disabled @endif
                            >
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>
                            </button>
                        </div>
                        
                        <div class="absolute inset-y-0 right-0 w-1/3 flex items-center justify-end pr-20">
                            <button 
                                wire:click="nextReel" 
                                class="text-white/50 hover:text-white p-4"
                                @if($currentReelIndex === count($reels) - 1) disabled @endif
                            >
                                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Seção de comentários (expansível) -->
                @if($showComments[$reel['id']] ?? false)
                    <div class="bg-black/90 backdrop-blur-sm max-h-64 overflow-y-auto">
                        <div class="p-4">
                            <!-- Lista de comentários -->
                            @if(count($reel['comments']) > 0)
                                <div class="space-y-3 mb-4">
                                    @foreach($reel['comments'] as $comment)
                                        <div class="flex space-x-3">
                                            <img 
                                                src="{{ $comment['user']['avatar'] }}" 
                                                alt="{{ $comment['user']['name'] }}"
                                                class="w-8 h-8 rounded-full"
                                            >
                                            <div class="flex-1">
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-white font-semibold text-sm">{{ $comment['user']['username'] }}</span>
                                                    <span class="text-white/60 text-xs">{{ $comment['created_at'] }}</span>
                                                </div>
                                                <p class="text-white text-sm">{{ $comment['body'] }}</p>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            <!-- Formulário de comentário -->
                            @auth
                                <div class="flex space-x-3">
                                    <img 
                                        src="{{ Auth::user()->userPhotos->first() ? Storage::url(Auth::user()->userPhotos->first()->photo_path) : asset('images/default-avatar.svg') }}" 
                                        alt="{{ Auth::user()->name }}"
                                        class="w-8 h-8 rounded-full"
                                    >
                                    <div class="flex-1 flex space-x-2">
                                        <input
                                            type="text"
                                            wire:model="newComment.{{ $reel['id'] }}"
                                            placeholder="Adicione um comentário..."
                                            class="flex-1 px-3 py-2 bg-white/10 border border-white/20 text-white placeholder-white/60 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                            wire:keydown.enter="addComment({{ $reel['id'] }})"
                                        />
                                        <button
                                            wire:click="addComment({{ $reel['id'] }})"
                                            class="px-3 py-2 text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                        >
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg>
                                        </button>
                                    </div>
                                </div>
                            @endauth
                        </div>
                    </div>
                @endif
            </div>
        @endforeach
    @else
        <!-- Estado vazio -->
        <div class="h-screen flex items-center justify-center text-white">
            <div class="text-center">
                <svg class="w-16 h-16 text-white/50 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>
                <h3 class="text-xl font-semibold mb-2">Nenhum reel disponível</h3>
                <p class="text-white/70">Seja o primeiro a criar um reel incrível!</p>
            </div>
        </div>
    @endif
</div>

<script>
document.addEventListener('livewire:navigated', function() {
    // Auto-play do vídeo atual
    const currentVideo = document.querySelector('#reel-video-{{ $reels[$currentReelIndex]['id'] ?? 0 }}');
    if (currentVideo) {
        currentVideo.play();
    }
});
</script>
