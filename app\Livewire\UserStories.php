<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Post;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class UserStories extends Component
{
    public $user;
    public $stories = [];
    public $highlights = [];
    public $isOwner = false;

    protected $listeners = [
        'story-created' => 'loadStories'
    ];

    public function mount($user = null)
    {
        if ($user) {
            $this->user = $user;
        } else {
            $this->user = Auth::user();
        }
        
        $this->isOwner = Auth::check() && Auth::id() === $this->user->id;
        $this->loadStories();
    }

    public function loadStories()
    {
        // Stories ativos (não expirados)
        $this->stories = Post::activeStories()
            ->where('user_id', $this->user->id)
            ->with(['user', 'user.userPhotos'])
            ->latest()
            ->get()
            ->map(function ($story) {
                return [
                    'id' => $story->id,
                    'content' => $story->content,
                    'image' => $story->image ? Storage::url($story->image) : null,
                    'video' => $story->video ? Storage::url($story->video) : null,
                    'created_at' => $story->created_at->diffForHumans(),
                    'expires_at' => $story->expires_at,
                    'views_count' => $story->getViewsCount(),
                    'music_title' => $story->music_title,
                    'filters' => $story->filters ?? [],
                    'is_highlight' => $story->is_highlight,
                ];
            })
            ->toArray();

        // Stories em destaque (highlights)
        $this->highlights = Post::highlights()
            ->where('user_id', $this->user->id)
            ->with(['user', 'user.userPhotos'])
            ->latest()
            ->get()
            ->map(function ($story) {
                return [
                    'id' => $story->id,
                    'content' => $story->content,
                    'image' => $story->image ? Storage::url($story->image) : null,
                    'video' => $story->video ? Storage::url($story->video) : null,
                    'created_at' => $story->created_at->diffForHumans(),
                    'views_count' => $story->getViewsCount(),
                    'music_title' => $story->music_title,
                    'filters' => $story->filters ?? [],
                ];
            })
            ->toArray();
    }

    public function addToHighlights($storyId)
    {
        if (!$this->isOwner) {
            $this->dispatch('notify', [
                'message' => 'Você não pode destacar este story.',
                'type' => 'error'
            ]);
            return;
        }

        $story = Post::find($storyId);
        
        if (!$story || $story->user_id !== Auth::id()) {
            $this->dispatch('notify', [
                'message' => 'Story não encontrado.',
                'type' => 'error'
            ]);
            return;
        }

        $story->update(['is_highlight' => true]);

        $this->dispatch('notify', [
            'message' => 'Story adicionado aos destaques!',
            'type' => 'success'
        ]);

        $this->loadStories();
    }

    public function removeFromHighlights($storyId)
    {
        if (!$this->isOwner) {
            $this->dispatch('notify', [
                'message' => 'Você não pode remover este destaque.',
                'type' => 'error'
            ]);
            return;
        }

        $story = Post::find($storyId);
        
        if (!$story || $story->user_id !== Auth::id()) {
            $this->dispatch('notify', [
                'message' => 'Story não encontrado.',
                'type' => 'error'
            ]);
            return;
        }

        $story->update(['is_highlight' => false]);

        $this->dispatch('notify', [
            'message' => 'Story removido dos destaques!',
            'type' => 'success'
        ]);

        $this->loadStories();
    }

    public function deleteStory($storyId)
    {
        if (!$this->isOwner) {
            $this->dispatch('notify', [
                'message' => 'Você não pode deletar este story.',
                'type' => 'error'
            ]);
            return;
        }

        $story = Post::find($storyId);
        
        if (!$story || $story->user_id !== Auth::id()) {
            $this->dispatch('notify', [
                'message' => 'Story não encontrado.',
                'type' => 'error'
            ]);
            return;
        }

        try {
            // Deletar arquivos de mídia
            if ($story->image) {
                Storage::disk('public')->delete($story->image);
            }
            if ($story->video) {
                Storage::disk('public')->delete($story->video);
            }

            $story->delete();

            $this->dispatch('notify', [
                'message' => 'Story deletado com sucesso!',
                'type' => 'success'
            ]);

            $this->loadStories();

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao deletar story.',
                'type' => 'error'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.user-stories');
    }
}
