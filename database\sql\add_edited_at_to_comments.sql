-- SQL para adicionar funcionalidade de edição de comentários
-- Execute este SQL no phpMyAdmin para implementar a funcionalidade

-- 1. Verificar se a coluna 'edited_at' já existe na tabela comments
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'comments' 
AND COLUMN_NAME = 'edited_at' 
AND TABLE_SCHEMA = DATABASE();

-- 2. Adicionar a coluna 'edited_at' se ela não existir
ALTER TABLE comments ADD COLUMN edited_at TIMESTAMP NULL AFTER body;

-- 3. Verificar a estrutura da tabela comments após a alteração
DESCRIBE comments;

-- 4. Verificar se há comentários existentes
SELECT COUNT(*) as total_comments FROM comments;

-- 5. Listar alguns comentários para verificar a nova coluna
SELECT 
    id,
    user_id,
    post_id,
    body,
    edited_at,
    created_at,
    updated_at
FROM comments 
ORDER BY created_at DESC 
LIMIT 10;

-- 6. Estatísticas de comentários por usuário
SELECT 
    u.name,
    u.username,
    COUNT(c.id) as total_comments
FROM users u
LEFT JOIN comments c ON u.id = c.user_id
GROUP BY u.id, u.name, u.username
HAVING total_comments > 0
ORDER BY total_comments DESC
LIMIT 10;

-- 7. Comentários mais recentes
SELECT 
    c.id,
    u.name as author,
    p.id as post_id,
    LEFT(c.body, 50) as comment_preview,
    c.created_at,
    c.edited_at
FROM comments c
JOIN users u ON c.user_id = u.id
JOIN posts p ON c.post_id = p.id
ORDER BY c.created_at DESC
LIMIT 20;

-- 8. Verificar integridade dos dados
-- Comentários órfãos (sem usuário)
SELECT COUNT(*) as orphaned_comments_no_user
FROM comments c
LEFT JOIN users u ON c.user_id = u.id
WHERE u.id IS NULL;

-- Comentários órfãos (sem post)
SELECT COUNT(*) as orphaned_comments_no_post
FROM comments c
LEFT JOIN posts p ON c.post_id = p.id
WHERE p.id IS NULL;

-- 9. Criar índice para melhor performance nas consultas por edited_at
CREATE INDEX idx_comments_edited_at ON comments(edited_at);

-- 10. Criar índice composto para consultas de comentários por post
CREATE INDEX idx_comments_post_created ON comments(post_id, created_at);

-- 11. Verificar índices criados
SHOW INDEX FROM comments;

-- 12. Estatísticas finais
SELECT 
    'Total de comentários' as metric,
    COUNT(*) as value
FROM comments
UNION ALL
SELECT 
    'Comentários editados' as metric,
    COUNT(*) as value
FROM comments 
WHERE edited_at IS NOT NULL
UNION ALL
SELECT 
    'Comentários não editados' as metric,
    COUNT(*) as value
FROM comments 
WHERE edited_at IS NULL;

-- 13. Exemplo de como marcar um comentário como editado (para teste)
-- UPDATE comments 
-- SET edited_at = NOW() 
-- WHERE id = [COMMENT_ID];

-- 14. Exemplo de como buscar comentários editados
-- SELECT 
--     c.id,
--     u.name,
--     c.body,
--     c.created_at,
--     c.edited_at,
--     TIMESTAMPDIFF(MINUTE, c.created_at, c.edited_at) as minutes_to_edit
-- FROM comments c
-- JOIN users u ON c.user_id = u.id
-- WHERE c.edited_at IS NOT NULL
-- ORDER BY c.edited_at DESC;

-- 15. Verificar se a migration foi aplicada corretamente
SELECT 
    'Migration Status' as status,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'comments' 
            AND COLUMN_NAME = 'edited_at'
        ) THEN 'SUCCESS - Column edited_at exists'
        ELSE 'ERROR - Column edited_at not found'
    END as result;
