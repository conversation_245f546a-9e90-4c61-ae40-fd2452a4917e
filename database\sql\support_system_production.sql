-- SQL para implementar o sistema de suporte em produção
-- Execute este arquivo via phpMy<PERSON><PERSON><PERSON> ou linha de comando MySQL

-- =====================================================
-- CRIAÇÃO DAS TABELAS
-- =====================================================

-- Tabela de tickets de suporte
CREATE TABLE IF NOT EXISTS `support_tickets` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `assigned_to` bigint(20) UNSIGNED DEFAULT NULL,
  `ticket_number` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `priority` enum('baixa','media','alta','urgente') NOT NULL DEFAULT 'media',
  `status` enum('aberto','em_andamento','aguardando_resposta','resolvido','fechado') NOT NULL DEFAULT 'aberto',
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `attachments` json DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `closed_at` timestamp NULL DEFAULT NULL,
  `rating` int(11) DEFAULT NULL,
  `rating_comment` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `support_tickets_ticket_number_unique` (`ticket_number`),
  KEY `support_tickets_user_id_foreign` (`user_id`),
  KEY `support_tickets_assigned_to_foreign` (`assigned_to`),
  KEY `support_tickets_user_id_status_index` (`user_id`,`status`),
  KEY `support_tickets_assigned_to_status_index` (`assigned_to`,`status`),
  KEY `support_tickets_ticket_number_index` (`ticket_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de mensagens dos tickets
CREATE TABLE IF NOT EXISTS `support_ticket_messages` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `ticket_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `message` text NOT NULL,
  `attachments` json DEFAULT NULL,
  `is_internal` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `support_ticket_messages_ticket_id_foreign` (`ticket_id`),
  KEY `support_ticket_messages_user_id_foreign` (`user_id`),
  KEY `support_ticket_messages_ticket_id_created_at_index` (`ticket_id`,`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de artigos de ajuda
CREATE TABLE IF NOT EXISTS `help_articles` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `category` varchar(255) NOT NULL,
  `tags` json DEFAULT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `is_published` tinyint(1) NOT NULL DEFAULT 1,
  `view_count` int(11) NOT NULL DEFAULT 0,
  `helpful_count` int(11) NOT NULL DEFAULT 0,
  `not_helpful_count` int(11) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `help_articles_slug_unique` (`slug`),
  KEY `help_articles_category_is_published_index` (`category`,`is_published`),
  KEY `help_articles_is_featured_is_published_index` (`is_featured`,`is_published`),
  KEY `help_articles_slug_index` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabela de itens FAQ
CREATE TABLE IF NOT EXISTS `faq_items` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `question` varchar(255) NOT NULL,
  `answer` text NOT NULL,
  `category` varchar(255) NOT NULL,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `is_published` tinyint(1) NOT NULL DEFAULT 1,
  `view_count` int(11) NOT NULL DEFAULT 0,
  `helpful_count` int(11) NOT NULL DEFAULT 0,
  `not_helpful_count` int(11) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `faq_items_category_is_published_index` (`category`,`is_published`),
  KEY `faq_items_is_featured_is_published_index` (`is_featured`,`is_published`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Adicionar chaves estrangeiras
ALTER TABLE `support_tickets`
  ADD CONSTRAINT `support_tickets_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `support_tickets_assigned_to_foreign` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `support_ticket_messages`
  ADD CONSTRAINT `support_ticket_messages_ticket_id_foreign` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `support_ticket_messages_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- =====================================================
-- INSERÇÃO DOS DADOS
-- =====================================================

-- Inserir artigos de ajuda
INSERT INTO help_articles (title, slug, content, category, tags, is_featured, is_published, sort_order, created_at, updated_at) VALUES
('Como criar seu perfil completo', 'como-criar-seu-perfil-completo', 'Bem-vindo ao nosso sistema! Criar um perfil completo é essencial para aproveitar todas as funcionalidades.\n\n1. Acesse ''Configurações'' no menu\n2. Clique em ''Perfil''\n3. Preencha todas as informações obrigatórias\n4. Adicione uma foto de perfil\n5. Escreva uma descrição interessante sobre você\n\nDicas importantes:\n- Use uma foto clara e atual\n- Seja honesto nas informações\n- Mantenha seu perfil sempre atualizado', 'primeiros_passos', '["perfil", "configuração", "primeiros passos"]', 1, 1, 1, NOW(), NOW()),

('Como usar o sistema de busca', 'como-usar-o-sistema-de-busca', 'O sistema de busca permite encontrar outros usuários com base em critérios específicos.\n\n1. Clique em ''Busca'' no menu lateral\n2. Use os filtros disponíveis:\n   - Idade\n   - Localização\n   - Interesses\n   - Status de relacionamento\n3. Clique em ''Buscar'' para ver os resultados\n4. Use o botão ''Limpar Filtros'' para resetar\n\nDicas:\n- Seja específico nos filtros para melhores resultados\n- Experimente diferentes combinações\n- Salve suas buscas favoritas', 'funcionalidades', '["busca", "filtros", "usuários"]', 1, 1, 2, NOW(), NOW()),

('Sistema de Radar - Como funciona', 'sistema-de-radar-como-funciona', 'O Radar é nossa funcionalidade tipo Tinder para encontrar pessoas próximas.\n\n1. Acesse ''Radar'' no menu\n2. Visualize os perfis sugeridos\n3. Deslize para a direita (ou clique no coração) se interessar\n4. Deslize para a esquerda (ou clique no X) para passar\n5. Quando há match mútuo, vocês podem conversar\n\nRecursos especiais:\n- Filtros de distância\n- Preferências de idade\n- Interesses em comum\n- Notificações de novos matches', 'funcionalidades', '["radar", "match", "proximidade"]', 1, 1, 3, NOW(), NOW()),

('Como enviar e receber mensagens', 'como-enviar-e-receber-mensagens', 'O sistema de mensagens permite comunicação direta entre usuários.\n\n1. Acesse ''Mensagens'' no menu\n2. Clique em ''Nova Conversa'' ou selecione uma existente\n3. Digite sua mensagem na caixa de texto\n4. Pressione Enter ou clique em ''Enviar''\n\nRecursos disponíveis:\n- Mensagens em tempo real\n- Indicadores de leitura\n- Histórico completo de conversas\n- Notificações de novas mensagens\n\nDicas de segurança:\n- Não compartilhe informações pessoais sensíveis\n- Reporte comportamentos inadequados\n- Use o bom senso nas conversas', 'mensagens', '["mensagens", "chat", "comunicação"]', 0, 1, 4, NOW(), NOW()),

('Como usar a Loja Virtual', 'como-usar-a-loja-virtual', 'Nossa loja oferece produtos digitais e físicos para melhorar sua experiência.\n\n1. Acesse ''Loja'' no menu\n2. Navegue pelas categorias ou use a busca\n3. Clique no produto para ver detalhes\n4. Adicione ao carrinho\n5. Finalize a compra no checkout\n\nTipos de produtos:\n- Assinaturas VIP\n- Conteúdo exclusivo\n- Acessórios e produtos físicos\n- Créditos e moedas virtuais\n\nFormas de pagamento:\n- Cartão de crédito/débito\n- PIX\n- Carteira digital interna', 'loja', '["loja", "compras", "pagamento"]', 0, 1, 5, NOW(), NOW()),

('Como participar de grupos', 'como-participar-de-grupos', 'Os grupos são comunidades temáticas onde você pode interagir com pessoas de interesses similares.\n\n1. Acesse ''Grupos'' no menu\n2. Navegue pelos grupos disponíveis\n3. Clique em ''Entrar'' no grupo desejado\n4. Aguarde aprovação (se necessário)\n5. Participe das discussões\n\nTipos de grupos:\n- Públicos (entrada livre)\n- Privados (necessita aprovação)\n- Premium (apenas para VIPs)\n\nDicas:\n- Leia as regras do grupo\n- Seja respeitoso nas interações\n- Contribua com conteúdo relevante', 'funcionalidades', '["grupos", "comunidade", "interação"]', 0, 1, 6, NOW(), NOW());

-- Inserir itens de FAQ
INSERT INTO faq_items (question, answer, category, is_featured, is_published, sort_order, created_at, updated_at) VALUES
('Como altero minha senha?', 'Para alterar sua senha:\n1. Vá em Configurações > Senha\n2. Digite sua senha atual\n3. Digite a nova senha\n4. Confirme a nova senha\n5. Clique em ''Salvar''\n\nA nova senha deve ter pelo menos 8 caracteres.', 'conta', 1, 1, 1, NOW(), NOW()),

('Como excluir minha conta?', 'Para excluir sua conta:\n1. Vá em Configurações > Conta\n2. Role até o final da página\n3. Clique em ''Excluir Conta''\n4. Confirme a ação\n\nATENÇÃO: Esta ação é irreversível e todos os seus dados serão perdidos.', 'conta', 1, 1, 2, NOW(), NOW()),

('Por que não consigo enviar mensagens?', 'Possíveis motivos:\n1. Você não tem uma assinatura ativa (usuários visitantes têm limitações)\n2. O usuário bloqueou você\n3. Problemas de conexão\n4. Sua conta pode estar temporariamente restrita\n\nTente atualizar a página ou entre em contato conosco.', 'mensagens', 1, 1, 3, NOW(), NOW()),

('Como funciona o sistema de pontos?', 'Você ganha pontos por:\n- Fazer login diário\n- Completar seu perfil\n- Receber curtidas\n- Participar de grupos\n- Criar conteúdo\n\nOs pontos podem ser usados para:\n- Destacar seu perfil\n- Comprar itens na loja\n- Acessar funcionalidades premium', 'funcionalidades', 1, 1, 4, NOW(), NOW()),

('Como reportar um usuário?', 'Para reportar comportamento inadequado:\n1. Vá ao perfil do usuário\n2. Clique nos três pontos (...)\n3. Selecione ''Reportar''\n4. Escolha o motivo\n5. Adicione detalhes se necessário\n6. Envie o report\n\nNossa equipe analisará em até 24 horas.', 'seguranca', 1, 1, 5, NOW(), NOW()),

('Quais são os benefícios da assinatura VIP?', 'Benefícios VIP incluem:\n- Mensagens ilimitadas\n- Perfil destacado nas buscas\n- Acesso a funcionalidades exclusivas\n- Suporte prioritário\n- Sem anúncios\n- Acesso a grupos premium\n- Estatísticas detalhadas do perfil', 'assinatura', 1, 1, 6, NOW(), NOW()),

('Como cancelar minha assinatura?', 'Para cancelar:\n1. Vá em Configurações > Assinatura\n2. Clique em ''Gerenciar Assinatura''\n3. Selecione ''Cancelar''\n4. Confirme o cancelamento\n\nVocê manterá os benefícios até o final do período pago.', 'assinatura', 0, 1, 7, NOW(), NOW()),

('Posso usar o site no celular?', 'Sim! Nosso site é totalmente responsivo e funciona perfeitamente em:\n- Smartphones\n- Tablets\n- Computadores\n- Smart TVs\n\nRecomendamos usar navegadores atualizados como Chrome, Firefox ou Safari.', 'tecnico', 0, 1, 8, NOW(), NOW()),

('Como funciona o sistema de carteira?', 'A carteira digital permite:\n- Receber pagamentos\n- Fazer transferências\n- Comprar na loja\n- Receber recompensas\n\nPara adicionar saldo:\n1. Acesse ''Carteira''\n2. Clique em ''Adicionar Saldo''\n3. Escolha o valor\n4. Selecione a forma de pagamento\n5. Confirme a transação', 'pagamento', 0, 1, 9, NOW(), NOW()),

('Como funciona o Monte Sua Noite?', 'O Monte Sua Noite é uma funcionalidade para planejar eventos:\n1. Acesse ''Monte Sua Noite''\n2. Clique em ''Criar Plano''\n3. Defina data, local e atividades\n4. Convide participantes\n5. Gerencie confirmações\n\nVocê pode criar planos públicos ou privados e receber patrocínios para posições especiais.', 'funcionalidades', 0, 1, 10, NOW(), NOW());

-- Inserir algumas categorias padrão para organização
-- (As categorias são definidas dinamicamente, mas isso serve como referência)

-- Comentário sobre as categorias disponíveis:
-- Artigos: primeiros_passos, funcionalidades, mensagens, loja, grupos, eventos, pagamento, tecnico
-- FAQ: conta, mensagens, funcionalidades, seguranca, assinatura, tecnico, pagamento

-- Fim do arquivo SQL
