<x-layouts.app.sidebar :title="__('Teste Layout Stories/Reels')">
<div class="bg-gray-50 dark:bg-zinc-900 min-h-screen">
    <div class="px-6 py-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Teste de Layout - Stories/Reels</h1>
        
        <!-- <PERSON>e básico da barra de stories -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4"><PERSON>a de Stories (Teste Básico)</h2>
            <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-zinc-700 p-4">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Stories</h3>
                    <div class="flex space-x-2">
                        <span class="text-sm text-purple-600">Ver todos</span>
                        <span class="text-gray-300">•</span>
                        <span class="text-sm text-blue-600">Reels</span>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4 overflow-x-auto pb-2">
                    <!-- Exemplo de story -->
                    <div class="flex-shrink-0">
                        <div class="flex flex-col items-center space-y-2">
                            <div class="relative">
                                <div class="w-16 h-16 rounded-full p-1 bg-gradient-to-tr from-purple-500 via-pink-500 to-orange-500">
                                    <div class="w-full h-full rounded-full bg-gray-300 dark:bg-gray-600"></div>
                                </div>
                            </div>
                            <span class="text-xs text-gray-700 dark:text-gray-300 max-w-16 truncate">Usuário</span>
                        </div>
                    </div>
                    
                    <!-- Mais exemplos -->
                    @for($i = 1; $i <= 5; $i++)
                    <div class="flex-shrink-0">
                        <div class="flex flex-col items-center space-y-2">
                            <div class="relative">
                                <div class="w-16 h-16 rounded-full p-1 bg-gray-300 dark:bg-gray-600">
                                    <div class="w-full h-full rounded-full bg-gray-200 dark:bg-gray-500"></div>
                                </div>
                            </div>
                            <span class="text-xs text-gray-700 dark:text-gray-300 max-w-16 truncate">User{{ $i }}</span>
                        </div>
                    </div>
                    @endfor
                </div>
            </div>
        </div>
        
        <!-- Teste básico de reel -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Reel (Teste Básico)</h2>
            <div class="w-full max-w-md mx-auto bg-black rounded-lg overflow-hidden" style="height: 400px;">
                <div class="relative h-full flex flex-col">
                    <!-- Simulação de vídeo -->
                    <div class="flex-1 bg-gradient-to-br from-purple-900 to-blue-900 flex items-center justify-center">
                        <div class="text-white text-center">
                            <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p>Simulação de Reel</p>
                        </div>
                    </div>
                    
                    <!-- Overlay com controles -->
                    <div class="absolute inset-0">
                        <!-- Header -->
                        <div class="absolute top-4 left-4 right-4 flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 rounded-full bg-white/20"></div>
                                <div>
                                    <p class="text-white font-semibold">@usuario</p>
                                    <p class="text-white/80 text-sm">2h atrás</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Controles laterais -->
                        <div class="absolute right-4 bottom-20 flex flex-col space-y-4">
                            <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            </div>
                        </div>
                        
                        <!-- Informações na parte inferior -->
                        <div class="absolute bottom-4 left-4 right-20">
                            <p class="text-white text-sm">Descrição do reel de teste...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Status dos componentes -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Status dos Componentes</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <h3 class="font-semibold text-green-800 dark:text-green-400">✅ Layout Básico</h3>
                    <p class="text-sm text-green-600 dark:text-green-300">Estrutura HTML funcionando</p>
                </div>
                <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <h3 class="font-semibold text-blue-800 dark:text-blue-400">🎨 CSS</h3>
                    <p class="text-sm text-blue-600 dark:text-blue-300">Tailwind CSS aplicado</p>
                </div>
            </div>
        </div>
        
        <!-- Links para componentes reais -->
        <div class="mt-8 bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Testar Componentes Reais</h2>
            <div class="flex space-x-4">
                <a href="{{ route('test-stories') }}" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg">
                    Componentes Livewire
                </a>
                <a href="{{ route('stories.index') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                    Página Stories
                </a>
                <a href="{{ route('reels.index') }}" class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg">
                    Página Reels
                </a>
            </div>
        </div>
    </div>
</div>
</x-layouts.app.sidebar>
