-- ALTERNATIVA: Sistema funcionará sem chaves estrangeiras
-- Execute este arquivo se as chaves estrangeiras continuarem dando problema
-- O sistema funcionará normalmente, apenas sem a proteção referencial do banco

-- Adicionar apenas índices para melhorar performance
CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_assigned_to ON support_tickets(assigned_to);
CREATE INDEX idx_support_ticket_messages_ticket_id ON support_ticket_messages(ticket_id);
CREATE INDEX idx_support_ticket_messages_user_id ON support_ticket_messages(user_id);

-- Comentário: O sistema funcionará perfeitamente sem as chaves estrangeiras
-- A integridade referencial será mantida pelo código PHP/Laravel
