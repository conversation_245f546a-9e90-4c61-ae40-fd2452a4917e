<x-layouts.app.sidebar :title="__('Teste Stories/Reels')">
<div class="bg-gray-50 dark:bg-zinc-900">
    <div class="px-6 py-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Teste do Sistema Stories/Reels</h1>
        
        <!-- Teste da barra de stories -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Barra de Stories</h2>
            <livewire:stories.stories-bar />
        </div>
        
        <!-- Teste dos botões de criação -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Botões de Criação</h2>
            <div class="flex space-x-4">
                <livewire:stories.create-story />
                <livewire:stories.create-reel />
            </div>
        </div>
        
        <!-- Teste dos componentes de usuário -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Componentes do Usuário</h2>
            <livewire:user-stories :user="auth()->user()" />
            <livewire:user-reels :user="auth()->user()" />
        </div>
        
        <!-- Links para páginas dedicadas -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Páginas Dedicadas</h2>
            <div class="flex space-x-4">
                <a href="{{ route('stories.index') }}" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg">
                    Ver Stories
                </a>
                <a href="{{ route('reels.index') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                    Ver Reels
                </a>
            </div>
        </div>
        
        <!-- Status do sistema -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg p-6 shadow-sm">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Status do Sistema</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">{{ \App\Models\Post::where('type', 'story')->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Total de Stories</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ \App\Models\Post::where('type', 'reel')->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Total de Reels</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ \App\Models\Post::activeStories()->count() }}</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">Stories Ativos</div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-layouts.app.sidebar>
