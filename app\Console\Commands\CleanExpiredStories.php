<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Post;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class CleanExpiredStories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stories:clean-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove stories expirados e seus arquivos de mídia';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Iniciando limpeza de stories expirados...');

        // Buscar stories expirados
        $expiredStories = Post::where('type', 'story')
            ->where('expires_at', '<', now())
            ->whereNotNull('expires_at')
            ->get();

        $deletedCount = 0;
        $deletedFiles = 0;

        foreach ($expiredStories as $story) {
            try {
                // Deletar arquivos de mídia
                if ($story->image && Storage::disk('public')->exists($story->image)) {
                    Storage::disk('public')->delete($story->image);
                    $deletedFiles++;
                    $this->line("Imagem deletada: {$story->image}");
                }

                if ($story->video && Storage::disk('public')->exists($story->video)) {
                    Storage::disk('public')->delete($story->video);
                    $deletedFiles++;
                    $this->line("Vídeo deletado: {$story->video}");
                }

                // Deletar o story
                $story->delete();
                $deletedCount++;

                $this->line("Story #{$story->id} deletado (usuário: {$story->user->name})");

            } catch (\Exception $e) {
                $this->error("Erro ao deletar story #{$story->id}: {$e->getMessage()}");
                Log::error('Erro ao deletar story expirado', [
                    'story_id' => $story->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        $this->info("Limpeza concluída!");
        $this->info("Stories deletados: {$deletedCount}");
        $this->info("Arquivos de mídia deletados: {$deletedFiles}");

        // Log da operação
        Log::info('Limpeza de stories expirados executada', [
            'stories_deleted' => $deletedCount,
            'files_deleted' => $deletedFiles
        ]);

        return Command::SUCCESS;
    }
}
