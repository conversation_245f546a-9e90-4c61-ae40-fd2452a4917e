<x-layouts.app.sidebar :title="__('Teste dos Ícones do Reels')">
<div class="px-6 py-6">
    <h1 class="text-2xl font-bold mb-8">Teste dos Ícones do Reels</h1>
    
    <h2 class="text-xl font-semibold mb-4">Ícones Outline (Padrão)</h2>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        <!-- Arrow Left -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.arrow-left class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">arrow-left</p>
        </div>

        <!-- Home -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.home class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">home</p>
        </div>

        <!-- Chevron Up -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.chevron-up class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">chevron-up</p>
        </div>

        <!-- Chevron Down -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.chevron-down class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">chevron-down</p>
        </div>

        <!-- Camera -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.camera class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">camera</p>
        </div>

        <!-- Star -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.star class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">star</p>
        </div>

        <!-- Clock -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.clock class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">clock</p>
        </div>

        <!-- Chat Bubble Left -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.chat-bubble-left class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">chat-bubble-left</p>
        </div>
    </div>

    <h2 class="text-xl font-semibold mb-4">Ícones Solid</h2>
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
        <!-- Arrow Left Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.arrow-left variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">arrow-left solid</p>
        </div>

        <!-- Home Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.home variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">home solid</p>
        </div>

        <!-- Chevron Up Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.chevron-up variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">chevron-up solid</p>
        </div>

        <!-- Chevron Down Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.chevron-down variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">chevron-down solid</p>
        </div>

        <!-- Camera Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.camera variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">camera solid</p>
        </div>

        <!-- Star Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.star variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">star solid</p>
        </div>

        <!-- Clock Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.clock variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">clock solid</p>
        </div>

        <!-- Chat Bubble Left Solid -->
        <div class="text-center p-4 border rounded-lg">
            <flux:icon.chat-bubble-left variant="solid" class="w-8 h-8 mx-auto mb-2" />
            <p class="text-sm">chat-bubble-left solid</p>
        </div>
    </div>
    
    <div class="mt-8 p-4 bg-green-100 border border-green-400 rounded-lg">
        <h2 class="text-lg font-semibold text-green-800 mb-2">✅ Teste Concluído</h2>
        <p class="text-green-700">Se você consegue ver todos os ícones acima (tanto outline quanto solid), a correção foi bem-sucedida!</p>
        <p class="text-green-600 text-sm mt-2">Agora usando a sintaxe correta do Flux UI: <code>flux:icon.nome-do-icone</code> com ícones do Heroicons.</p>
        <div class="mt-4 space-x-4">
            <a href="{{ route('reels.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                <flux:icon.arrow-left class="w-4 h-4 mr-2" />
                Ir para Reels
            </a>
            <a href="{{ route('stories.index') }}" class="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600">
                <flux:icon.camera class="w-4 h-4 mr-2" />
                Ir para Stories
            </a>
        </div>
    </div>
</div>
</x-layouts.app.sidebar>
