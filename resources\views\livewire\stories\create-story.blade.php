<div>
    <!-- <PERSON>tão para abrir modal -->
    <button
        wire:click="openModal"
        class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold px-6 py-3 rounded-full shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center"
    >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
        </svg>
        Criar Story
    </button>

    <!-- Modal para criar story -->
    <div x-data="{ showModal: @entangle('showModal') }" x-show="showModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="showModal = false"></div>
        <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-zinc-800 shadow-xl rounded-lg">
        <div class="mb-6">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Criar Novo Story</h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Compartilhe um momento especial</p>
                </div>
            </div>
        </div>

        <form wire:submit="createStory">
            <div class="space-y-6">
                <!-- Conteúdo do story -->
                <div>
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Conteúdo (opcional)</label>
                        <textarea wire:model="content" placeholder="Escreva algo sobre seu story..." rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-zinc-700 dark:text-white resize-none"></textarea>
                        @error('content') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                    </div>
                </div>

                <!-- Upload de mídia -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Upload de imagem -->
                    <div>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Imagem</label>
                            <input type="file" wire:model="image" accept="image/*" class="w-full file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100 dark:file:bg-purple-900 dark:file:text-purple-300">
                            @error('image') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                        
                        @if ($image)
                            <div class="mt-2">
                                <img src="{{ $image->temporaryUrl() }}" class="w-full h-32 object-cover rounded-lg">
                            </div>
                        @endif
                    </div>

                    <!-- Upload de vídeo -->
                    <div>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Vídeo</label>
                            <input type="file" wire:model="video" accept="video/*" class="w-full file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-pink-50 file:text-pink-700 hover:file:bg-pink-100 dark:file:bg-pink-900 dark:file:text-pink-300">
                            @error('video') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                        
                        @if ($video)
                            <div class="mt-2">
                                <video controls class="w-full h-32 object-cover rounded-lg">
                                    <source src="{{ $video->temporaryUrl() }}" type="video/mp4">
                                </video>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Configurações do story -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Duração -->
                    <div>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Duração (horas)</label>
                            <select wire:model="duration" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-zinc-700 dark:text-white">
                                <option value="1">1 hora</option>
                                <option value="6">6 horas</option>
                                <option value="12">12 horas</option>
                                <option value="24" selected>24 horas</option>
                                <option value="48">48 horas</option>
                                <option value="72">72 horas</option>
                                <option value="168">7 dias</option>
                            </select>
                            @error('duration') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>

                    <!-- Música (opcional) -->
                    <div>
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Música (opcional)</label>
                            <input type="text" wire:model="music_title" placeholder="Nome da música" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-zinc-700 dark:text-white">
                        </div>
                    </div>
                </div>

                <!-- Filtros disponíveis -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Filtros</label>
                    <div class="flex flex-wrap gap-2 mt-2">
                        @foreach(['vintage', 'bw', 'sepia', 'bright', 'contrast', 'warm', 'cool'] as $filter)
                            <button 
                                type="button"
                                wire:click="addFilter('{{ $filter }}')"
                                class="px-3 py-1 text-xs rounded-full border transition-colors
                                    {{ in_array($filter, $filters) 
                                        ? 'bg-purple-100 border-purple-300 text-purple-700' 
                                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200' 
                                    }}"
                            >
                                {{ ucfirst($filter) }}
                            </button>
                        @endforeach
                    </div>
                </div>

                <!-- Loading indicator -->
                <div wire:loading wire:target="createStory" class="flex items-center justify-center py-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    <span class="ml-2 text-gray-600">Criando story...</span>
                </div>
            </div>

            <div class="mt-6 flex justify-between">
                <div class="flex justify-between w-full">
                    <button type="button" wire:click="closeModal" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        Cancelar
                    </button>

                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50" wire:loading.attr="disabled">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                        Publicar Story
                    </button>
                </div>
            </div>
        </form>
        </div>
    </div>
</div>
</div>
