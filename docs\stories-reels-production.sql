-- S<PERSON> para implementar Stories/Reels em produção
-- Execute este script no phpMyAdmin do KingHost

-- 1. Adicionar campos para Stories/Reels na tabela posts
ALTER TABLE `posts` 
ADD COLUMN `type` ENUM('post', 'story', 'reel') NOT NULL DEFAULT 'post' AFTER `video`,
ADD COLUMN `expires_at` TIMESTAMP NULL AFTER `type`,
ADD COLUMN `story_views` JSON NULL AFTER `expires_at`,
ADD COLUMN `is_highlight` BOOLEAN NOT NULL DEFAULT FALSE AFTER `story_views`,
ADD COLUMN `music_url` VARCHAR(255) NULL AFTER `is_highlight`,
ADD COLUMN `music_title` VARCHAR(255) NULL AFTER `music_url`,
ADD COLUMN `filters` JSON NULL AFTER `music_title`,
ADD COLUMN `duration` INT NULL AFTER `filters`;

-- 2. Adicionar índices para performance
ALTER TABLE `posts` 
ADD INDEX `idx_posts_type_created` (`type`, `created_at`),
ADD INDEX `idx_posts_user_type` (`user_id`, `type`),
ADD INDEX `idx_posts_expires_at` (`expires_at`);

-- 3. Atualizar registros existentes (todos os posts atuais são do tipo 'post')
UPDATE `posts` SET `type` = 'post' WHERE `type` IS NULL;

-- 4. Verificar se as alterações foram aplicadas corretamente
SELECT 
    COLUMN_NAME, 
    DATA_TYPE, 
    IS_NULLABLE, 
    COLUMN_DEFAULT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'posts' 
AND TABLE_SCHEMA = DATABASE()
AND COLUMN_NAME IN ('type', 'expires_at', 'story_views', 'is_highlight', 'music_url', 'music_title', 'filters', 'duration')
ORDER BY ORDINAL_POSITION;

-- 5. Verificar índices criados
SHOW INDEX FROM `posts` WHERE Key_name IN ('idx_posts_type_created', 'idx_posts_user_type', 'idx_posts_expires_at');

-- 6. Exemplo de consultas que serão usadas pelo sistema

-- Buscar stories ativos
SELECT * FROM `posts` 
WHERE `type` = 'story' 
AND (`expires_at` IS NULL OR `expires_at` > NOW())
ORDER BY `created_at` DESC;

-- Buscar reels
SELECT * FROM `posts` 
WHERE `type` = 'reel'
ORDER BY `created_at` DESC;

-- Buscar stories em destaque
SELECT * FROM `posts` 
WHERE `type` = 'story' 
AND `is_highlight` = TRUE
ORDER BY `created_at` DESC;

-- Buscar stories expirados (para limpeza)
SELECT * FROM `posts` 
WHERE `type` = 'story' 
AND `expires_at` < NOW()
AND `expires_at` IS NOT NULL;

-- 7. Script de limpeza manual (executar periodicamente)
-- ATENÇÃO: Este comando deleta permanentemente stories expirados
-- DELETE FROM `posts` 
-- WHERE `type` = 'story' 
-- AND `expires_at` < NOW()
-- AND `expires_at` IS NOT NULL;

-- 8. Estatísticas do sistema
SELECT 
    `type`,
    COUNT(*) as total,
    COUNT(CASE WHEN `image` IS NOT NULL THEN 1 END) as with_image,
    COUNT(CASE WHEN `video` IS NOT NULL THEN 1 END) as with_video,
    COUNT(CASE WHEN `music_title` IS NOT NULL THEN 1 END) as with_music
FROM `posts` 
GROUP BY `type`;

-- 9. Verificar stories ativos por usuário
SELECT 
    u.username,
    u.name,
    COUNT(p.id) as active_stories
FROM `users` u
LEFT JOIN `posts` p ON u.id = p.user_id 
    AND p.type = 'story' 
    AND (p.expires_at IS NULL OR p.expires_at > NOW())
GROUP BY u.id, u.username, u.name
HAVING active_stories > 0
ORDER BY active_stories DESC;

-- 10. Verificar reels por usuário
SELECT 
    u.username,
    u.name,
    COUNT(p.id) as total_reels,
    SUM(p.likes_count) as total_likes
FROM `users` u
LEFT JOIN `posts` p ON u.id = p.user_id AND p.type = 'reel'
GROUP BY u.id, u.username, u.name
HAVING total_reels > 0
ORDER BY total_reels DESC;

-- NOTAS IMPORTANTES:
-- 1. Faça backup da tabela posts antes de executar as alterações
-- 2. Execute os comandos um por vez e verifique os resultados
-- 3. O campo story_views armazena um JSON com IDs dos usuários que visualizaram
-- 4. O campo filters armazena um JSON com os filtros aplicados
-- 5. O campo expires_at define quando o story expira (NULL = nunca expira)
-- 6. Configure um cron job para executar a limpeza de stories expirados periodicamente
