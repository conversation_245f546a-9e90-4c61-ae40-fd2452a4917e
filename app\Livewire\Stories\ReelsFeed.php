<?php

namespace App\Livewire\Stories;

use Livewire\Component;
use App\Models\Post;
use App\Models\Like;
use App\Models\Comment;
use App\Models\UserPoint;
use App\Models\Notification;
use App\Services\ContentProcessor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class ReelsFeed extends Component
{
    public $reels = [];
    public $currentReelIndex = 0;
    public $newComment = [];
    public $showComments = [];

    protected $listeners = [
        'reel-created' => 'loadReels',
        'next-reel' => 'nextReel',
        'previous-reel' => 'previousReel'
    ];

    public function mount()
    {
        $this->loadReels();
    }

    public function loadReels()
    {
        $this->reels = Post::reels()
            ->with([
                'user',
                'user.userPhotos',
                'likedByUsers',
                'comments.user.userPhotos',
                'comments' => function($query) {
                    $query->latest()->limit(3);
                }
            ])
            ->latest()
            ->limit(50) // Carregar 50 reels por vez
            ->get()
            ->map(function ($reel) {
                return [
                    'id' => $reel->id,
                    'content' => $reel->content,
                    'video' => Storage::url($reel->video),
                    'music_title' => $reel->music_title,
                    'music_url' => $reel->music_url,
                    'filters' => $reel->filters ?? [],
                    'duration' => $reel->duration,
                    'created_at' => $reel->created_at->diffForHumans(),
                    'likes_count' => $reel->likedByUsers->count(),
                    'comments_count' => $reel->comments->count(),
                    'is_liked' => Auth::check() ? $reel->isLikedBy(Auth::user()) : false,
                    'user' => [
                        'id' => $reel->user->id,
                        'name' => $reel->user->name,
                        'username' => $reel->user->username,
                        'avatar' => $reel->user->userPhotos->first() 
                            ? Storage::url($reel->user->userPhotos->first()->photo_path)
                            : asset('images/default-avatar.svg')
                    ],
                    'comments' => $reel->comments->map(function ($comment) {
                        return [
                            'id' => $comment->id,
                            'body' => $comment->body,
                            'created_at' => $comment->created_at->diffForHumans(),
                            'user' => [
                                'id' => $comment->user->id,
                                'name' => $comment->user->name,
                                'username' => $comment->user->username,
                                'avatar' => $comment->user->userPhotos->first() 
                                    ? Storage::url($comment->user->userPhotos->first()->photo_path)
                                    : asset('images/default-avatar.svg')
                            ]
                        ];
                    })->toArray()
                ];
            })
            ->toArray();

        Log::info('Reels loaded', ['count' => count($this->reels)]);
    }

    public function toggleLike($reelId)
    {
        if (!Auth::check()) {
            $this->dispatch('notify', [
                'message' => 'Você precisa estar logado para curtir.',
                'type' => 'error'
            ]);
            return;
        }

        $reel = Post::find($reelId);
        if (!$reel) {
            return;
        }

        $user = Auth::user();
        $wasLiked = $reel->isLikedBy($user);

        if ($wasLiked) {
            // Remove curtida
            $reel->likedByUsers()->detach($user->id);

            // Remove notificação
            Notification::where([
                'sender_id' => $user->id,
                'post_id' => $reel->id,
                'type' => 'like'
            ])->delete();

            // Remove pontos (apenas se o reel não for do próprio usuário)
            if ($reel->user_id !== $user->id) {
                UserPoint::removePoints(
                    $reel->user_id,
                    'like',
                    5,
                    "Perdeu curtida de {$user->name}",
                    $reel->id,
                    Post::class
                );
            }
        } else {
            // Adiciona curtida
            $reel->likedByUsers()->attach($user->id);

            // Adiciona pontos ao usuário que curtiu
            UserPoint::addPoints(
                $user->id,
                'like',
                2,
                "Curtiu reel de " . ($reel->user_id === $user->id ? "sua autoria" : $reel->user->name),
                $reel->id,
                Post::class
            );

            // Adiciona pontos ao autor do reel (se não for o mesmo usuário)
            if ($reel->user_id !== $user->id) {
                UserPoint::addPoints(
                    $reel->user_id,
                    'like_received',
                    5,
                    "Recebeu curtida de {$user->name}",
                    $reel->id,
                    Post::class
                );

                // Cria notificação
                Notification::create([
                    'user_id' => $reel->user_id,
                    'sender_id' => $user->id,
                    'type' => 'like',
                    'post_id' => $reel->id
                ]);
            }

            // Dispara animação de recompensa
            $this->dispatch('reward-earned', points: 2);
        }

        // Recarregar reels para atualizar contadores
        $this->loadReels();
    }

    public function addComment($reelId)
    {
        if (!Auth::check()) {
            $this->dispatch('notify', [
                'message' => 'Você precisa estar logado para comentar.',
                'type' => 'error'
            ]);
            return;
        }

        $this->validate([
            "newComment.$reelId" => 'required|min:1|max:500'
        ]);

        $reel = Post::findOrFail($reelId);
        $user = Auth::user();

        $comment = Comment::create([
            'user_id' => $user->id,
            'post_id' => $reelId,
            'body' => $this->newComment[$reelId]
        ]);

        // Processar hashtags e menções para o comentário
        if ($comment && !empty($this->newComment[$reelId])) {
            ContentProcessor::processHashtags($this->newComment[$reelId], $comment);
            ContentProcessor::processMentions($this->newComment[$reelId], $comment, Auth::id());
        }

        // Adiciona pontos ao usuário que comentou
        UserPoint::addPoints(
            $user->id,
            'comment',
            5,
            "Comentou no reel de " . ($reel->user_id === $user->id ? "sua autoria" : $reel->user->name),
            $comment->id,
            Comment::class
        );

        // Adiciona pontos ao autor do reel (se não for o mesmo usuário)
        if ($reel->user_id !== $user->id) {
            UserPoint::addPoints(
                $reel->user_id,
                'comment_received',
                3,
                "Recebeu comentário de {$user->name}",
                $comment->id,
                Comment::class
            );

            // Cria notificação
            Notification::create([
                'user_id' => $reel->user_id,
                'sender_id' => $user->id,
                'type' => 'comment',
                'post_id' => $reel->id,
                'comment_id' => $comment->id
            ]);
        }

        // Limpa o campo de comentário
        $this->newComment[$reelId] = '';

        // Recarregar reels para mostrar novo comentário
        $this->loadReels();

        $this->dispatch('notify', [
            'message' => 'Comentário adicionado!',
            'type' => 'success'
        ]);
    }

    public function toggleComments($reelId)
    {
        $this->showComments[$reelId] = !($this->showComments[$reelId] ?? false);
    }

    public function nextReel()
    {
        if ($this->currentReelIndex < count($this->reels) - 1) {
            $this->currentReelIndex++;
        }
    }

    public function previousReel()
    {
        if ($this->currentReelIndex > 0) {
            $this->currentReelIndex--;
        }
    }

    public function render()
    {
        return view('livewire.stories.reels-feed');
    }
}
