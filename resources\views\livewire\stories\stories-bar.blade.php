<div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-gray-200 dark:border-zinc-700 p-4 mb-6">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Stories</h3>
        <div class="flex space-x-2">
            <a href="{{ route('stories.index') }}" class="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300">
                Ver todos
            </a>
            <span class="text-gray-300">•</span>
            <a href="{{ route('reels.index') }}" class="text-sm text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
                Reels
            </a>
        </div>
    </div>

    <div class="flex items-center space-x-4 overflow-x-auto scrollbar-hide pb-2">
        <!-- Botão para criar story -->
        @if($showCreateButton)
            <div class="flex-shrink-0">
                <livewire:stories.create-story />
            </div>
        @endif

        <!-- Lista de stories -->
        @if(count($stories) > 0)
            @foreach($stories as $userStory)
                <div class="flex-shrink-0">
                    <button 
                        wire:click="viewStory({{ $userStory['user']['id'] }})"
                        class="flex flex-col items-center space-y-2 group"
                    >
                        <!-- Avatar com indicador de story -->
                        <div class="relative">
                            <div class="w-16 h-16 rounded-full p-1 
                                {{ $userStory['has_unviewed'] 
                                    ? 'bg-gradient-to-tr from-purple-500 via-pink-500 to-orange-500' 
                                    : 'bg-gray-300 dark:bg-gray-600' 
                                }}">
                                <img 
                                    src="{{ $userStory['user']['avatar'] }}" 
                                    alt="{{ $userStory['user']['name'] }}"
                                    class="w-full h-full rounded-full object-cover border-2 border-white dark:border-zinc-800"
                                >
                            </div>
                            
                            <!-- Indicador de quantidade de stories -->
                            @if($userStory['total_stories'] > 1)
                                <div class="absolute -top-1 -right-1 bg-purple-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                    {{ $userStory['total_stories'] }}
                                </div>
                            @endif

                            <!-- Indicador de próprio story -->
                            @if($userStory['user']['id'] == Auth::id())
                                <div class="absolute -bottom-1 -right-1 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center">
                                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                    </svg>
                                </div>
                            @endif
                        </div>
                        
                        <!-- Nome do usuário -->
                        <div class="text-center">
                            <span class="text-xs text-gray-700 dark:text-gray-300 max-w-16 truncate block">
                                {{ $userStory['user']['id'] == Auth::id() ? 'Seu story' : $userStory['user']['username'] }}
                            </span>
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $userStory['latest_story']['created_at'] }}
                            </span>
                        </div>
                    </button>
                </div>
            @endforeach
        @else
            <!-- Estado vazio -->
            <div class="flex-1 flex items-center justify-center py-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gray-100 dark:bg-zinc-700 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-8 h-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm">Nenhum story disponível</p>
                    <p class="text-xs text-gray-400 dark:text-gray-500">Seja o primeiro a compartilhar!</p>
                </div>
            </div>
        @endif
    </div>

    <!-- Indicadores de funcionalidades -->
    <div class="mt-4 flex items-center justify-center space-x-6 text-xs text-gray-500 dark:text-gray-400">
        <div class="flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>24h temporário</span>
        </div>
        <div class="flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
            </svg>
            <span>Ganhe pontos</span>
        </div>
        <div class="flex items-center space-x-1">
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
            <span>Veja quem visualizou</span>
        </div>
    </div>

    <!-- CSS inline para esconder scrollbar -->
    <style>
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
    </style>
</div>
