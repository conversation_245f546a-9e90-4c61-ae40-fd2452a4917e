-- Parte 1: <PERSON>ria<PERSON> das tabelas do sistema de suporte
-- Execute este arquivo primeiro via phpMyAdmin

-- Tabela de tickets de suporte
CREATE TABLE IF NOT EXISTS `support_tickets` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `assigned_to` bigint(20) UNSIGNED DEFAULT NULL,
  `ticket_number` varchar(255) NOT NULL,
  `category` varchar(255) NOT NULL,
  `priority` enum('baixa','media','alta','urgente') NOT NULL DEFAULT 'media',
  `status` enum('aberto','em_andamento','aguardando_resposta','resolvido','fechado') NOT NULL DEFAULT 'aberto',
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `attachments` json DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `closed_at` timestamp NULL DEFAULT NULL,
  `rating` int(11) DEFAULT NULL,
  `rating_comment` text DEFAULT NULL,
  `is_premium` tinyint(1) NOT NULL DEFAULT 0,
  `premium_amount` decimal(8,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `support_tickets_ticket_number_unique` (`ticket_number`),
  KEY `support_tickets_user_id_foreign` (`user_id`),
  KEY `support_tickets_assigned_to_foreign` (`assigned_to`),
  KEY `support_tickets_user_id_status_index` (`user_id`,`status`),
  KEY `support_tickets_assigned_to_status_index` (`assigned_to`,`status`),
  KEY `support_tickets_ticket_number_index` (`ticket_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
