<?php

namespace App\Livewire\Stories;

use Livewire\Component;
use App\Models\Post;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class StoriesBar extends Component
{
    public $stories = [];
    public $showCreateButton = true;

    protected $listeners = [
        'story-created' => 'loadStories',
        'reel-created' => 'loadStories'
    ];

    public function mount()
    {
        $this->loadStories();
    }

    public function loadStories()
    {
        if (!Auth::check()) {
            $this->stories = [];
            return;
        }

        // Buscar stories ativos dos usuários seguidos + próprios stories
        $followingIds = Auth::user()->following()->pluck('users.id')->toArray();
        $followingIds[] = Auth::id(); // Incluir próprios stories

        $this->stories = Post::activeStories()
            ->whereIn('user_id', $followingIds)
            ->with(['user', 'user.userPhotos'])
            ->orderBy('created_at', 'desc')
            ->limit(10) // Limitar a 10 stories na barra
            ->get()
            ->groupBy('user_id')
            ->map(function ($userStories) {
                $user = $userStories->first()->user;
                $hasUnviewed = $userStories->some(function ($story) {
                    return !$story->hasBeenViewedBy(Auth::id());
                });

                return [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'username' => $user->username,
                        'avatar' => $user->userPhotos->first() 
                            ? Storage::url($user->userPhotos->first()->photo_path)
                            : asset('images/default-avatar.svg')
                    ],
                    'has_unviewed' => $hasUnviewed,
                    'total_stories' => $userStories->count(),
                    'latest_story' => [
                        'id' => $userStories->first()->id,
                        'created_at' => $userStories->first()->created_at->diffForHumans()
                    ]
                ];
            })
            ->values()
            ->toArray();
    }

    public function viewStory($userId)
    {
        $this->dispatch('view-story', userId: $userId)->to('stories.stories-feed');
    }

    public function render()
    {
        return view('livewire.stories.stories-bar');
    }
}
