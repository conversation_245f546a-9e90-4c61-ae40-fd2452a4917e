<?php

namespace App\Livewire\Stories;

use Livewire\Component;
use App\Models\Post;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class StoriesFeed extends Component
{
    public $stories = [];
    public $currentStoryIndex = 0;
    public $currentStory = null;
    public $showViewer = false;
    public $autoPlay = true;
    public $storyProgress = 0;

    protected $listeners = [
        'story-created' => 'loadStories',
        'view-story' => 'viewStory',
        'next-story' => 'nextStory',
        'previous-story' => 'previousStory',
        'close-viewer' => 'closeViewer'
    ];

    public function mount()
    {
        $this->loadStories();
    }

    public function loadStories()
    {
        if (!Auth::check()) {
            $this->stories = [];
            return;
        }

        // Buscar stories ativos dos usuários seguidos + próprios stories
        $followingIds = Auth::user()->following()->pluck('users.id')->toArray();
        $followingIds[] = Auth::id(); // Incluir próprios stories

        $this->stories = Post::activeStories()
            ->whereIn('user_id', $followingIds)
            ->with(['user', 'user.userPhotos'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->groupBy('user_id')
            ->map(function ($userStories) {
                $user = $userStories->first()->user;
                $hasUnviewed = $userStories->some(function ($story) {
                    return !$story->hasBeenViewedBy(Auth::id());
                });

                return [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'username' => $user->username,
                        'avatar' => $user->userPhotos->first() 
                            ? Storage::url($user->userPhotos->first()->photo_path)
                            : asset('images/default-avatar.svg')
                    ],
                    'stories' => $userStories->map(function ($story) {
                        return [
                            'id' => $story->id,
                            'content' => $story->content,
                            'image' => $story->image ? Storage::url($story->image) : null,
                            'video' => $story->video ? Storage::url($story->video) : null,
                            'created_at' => $story->created_at,
                            'expires_at' => $story->expires_at,
                            'views_count' => $story->getViewsCount(),
                            'has_viewed' => $story->hasBeenViewedBy(Auth::id()),
                            'music_title' => $story->music_title,
                            'filters' => $story->filters ?? [],
                        ];
                    })->values()->toArray(),
                    'has_unviewed' => $hasUnviewed,
                    'total_stories' => $userStories->count()
                ];
            })
            ->values()
            ->toArray();

        Log::info('Stories loaded', ['count' => count($this->stories)]);
    }

    public function viewStory($userId, $storyIndex = 0)
    {
        // Encontrar o usuário nas stories
        $userStoryIndex = collect($this->stories)->search(function ($item) use ($userId) {
            return $item['user']['id'] == $userId;
        });

        if ($userStoryIndex === false) {
            return;
        }

        $this->currentStoryIndex = $userStoryIndex;
        $this->currentStory = $this->stories[$userStoryIndex];
        $this->showViewer = true;

        // Marcar como visualizado se não for próprio story
        if ($userId != Auth::id()) {
            $storyId = $this->currentStory['stories'][$storyIndex]['id'] ?? null;
            if ($storyId) {
                $story = Post::find($storyId);
                if ($story) {
                    $story->addView(Auth::id());
                }
            }
        }

        Log::info('Viewing story', [
            'user_id' => $userId,
            'story_index' => $storyIndex,
            'viewer_id' => Auth::id()
        ]);
    }

    public function nextStory()
    {
        if ($this->currentStoryIndex < count($this->stories) - 1) {
            $this->currentStoryIndex++;
            $this->currentStory = $this->stories[$this->currentStoryIndex];
            
            // Marcar primeiro story do próximo usuário como visualizado
            $this->markCurrentStoryAsViewed();
        } else {
            $this->closeViewer();
        }
    }

    public function previousStory()
    {
        if ($this->currentStoryIndex > 0) {
            $this->currentStoryIndex--;
            $this->currentStory = $this->stories[$this->currentStoryIndex];
            
            // Marcar primeiro story do usuário anterior como visualizado
            $this->markCurrentStoryAsViewed();
        }
    }

    public function closeViewer()
    {
        $this->showViewer = false;
        $this->currentStory = null;
        $this->currentStoryIndex = 0;
        $this->storyProgress = 0;
        
        // Recarregar stories para atualizar status de visualização
        $this->loadStories();
    }

    private function markCurrentStoryAsViewed()
    {
        if (!$this->currentStory || $this->currentStory['user']['id'] == Auth::id()) {
            return;
        }

        $storyId = $this->currentStory['stories'][0]['id'] ?? null;
        if ($storyId) {
            $story = Post::find($storyId);
            if ($story) {
                $story->addView(Auth::id());
            }
        }
    }

    public function deleteStory($storyId)
    {
        $story = Post::find($storyId);
        
        if (!$story || $story->user_id !== Auth::id()) {
            $this->dispatch('notify', [
                'message' => 'Você não pode deletar este story.',
                'type' => 'error'
            ]);
            return;
        }

        try {
            // Deletar arquivos de mídia
            if ($story->image) {
                Storage::disk('public')->delete($story->image);
            }
            if ($story->video) {
                Storage::disk('public')->delete($story->video);
            }

            $story->delete();

            $this->dispatch('notify', [
                'message' => 'Story deletado com sucesso!',
                'type' => 'success'
            ]);

            $this->loadStories();
            $this->closeViewer();

        } catch (\Exception $e) {
            Log::error('Error deleting story', [
                'story_id' => $storyId,
                'error' => $e->getMessage()
            ]);

            $this->dispatch('notify', [
                'message' => 'Erro ao deletar story.',
                'type' => 'error'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.stories.stories-feed');
    }
}
