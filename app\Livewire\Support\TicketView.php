<?php

namespace App\Livewire\Support;

use App\Models\SupportTicket;
use App\Models\SupportTicketMessage;
use App\Models\User;
use App\Notifications\SupportTicketNewMessage;
use Livewire\Component;
use Livewire\WithFileUploads;

class TicketView extends Component
{
    use WithFileUploads;

    public SupportTicket $ticket;
    public $newMessage = '';
    public $attachments = [];
    public $rating = null;
    public $ratingComment = '';
    public $showRatingModal = false;

    protected $rules = [
        'newMessage' => 'required|string|min:5',
        'attachments.*' => 'nullable|file|max:10240|mimes:jpg,jpeg,png,pdf,doc,docx,txt',
        'rating' => 'required|integer|min:1|max:5',
        'ratingComment' => 'nullable|string|max:500',
    ];

    protected $messages = [
        'newMessage.required' => 'A mensagem é obrigatória.',
        'newMessage.min' => 'A mensagem deve ter pelo menos 5 caracteres.',
        'rating.required' => 'Selecione uma avaliação.',
        'rating.min' => 'A avaliação deve ser entre 1 e 5 estrelas.',
        'rating.max' => 'A avaliação deve ser entre 1 e 5 estrelas.',
    ];

    public function mount(SupportTicket $ticket)
    {
        // Verificar se o usuário pode ver este ticket
        if ($ticket->user_id !== auth()->id() && auth()->user()->role !== 'admin') {
            abort(403, 'Acesso negado.');
        }

        $this->ticket = $ticket;
    }

    public function sendMessage()
    {
        $this->validate(['newMessage' => $this->rules['newMessage']]);

        try {
            // Upload attachments
            $uploadedFiles = [];
            if (!empty($this->attachments)) {
                foreach ($this->attachments as $file) {
                    $path = $file->store('support-tickets', 'public');
                    $uploadedFiles[] = [
                        'name' => $file->getClientOriginalName(),
                        'path' => $path,
                        'size' => $file->getSize(),
                        'type' => $file->getMimeType(),
                    ];
                }
            }

            // Create message
            $message = SupportTicketMessage::create([
                'ticket_id' => $this->ticket->id,
                'user_id' => auth()->id(),
                'message' => $this->newMessage,
                'attachments' => $uploadedFiles,
                'is_internal' => false,
            ]);

            // Update ticket status if needed
            if ($this->ticket->status === 'aguardando_resposta') {
                $this->ticket->update(['status' => 'em_andamento']);
            }

            // Notify relevant users
            if (auth()->user()->role === 'admin') {
                // Admin replied, notify user
                $this->ticket->user->notify(new SupportTicketNewMessage($this->ticket, $message));
            } else {
                // User replied, notify admins
                $admins = User::where('role', 'admin')->get();
                foreach ($admins as $admin) {
                    $admin->notify(new SupportTicketNewMessage($this->ticket, $message));
                }
            }

            $this->dispatch('notify', [
                'message' => 'Mensagem enviada com sucesso!',
                'type' => 'success'
            ]);

            $this->reset(['newMessage', 'attachments']);
            $this->ticket->refresh();

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao enviar mensagem. Tente novamente.',
                'type' => 'error'
            ]);
        }
    }

    public function removeAttachment($index)
    {
        unset($this->attachments[$index]);
        $this->attachments = array_values($this->attachments);
    }

    public function openRatingModal()
    {
        if (!$this->ticket->canBeRated()) {
            $this->dispatch('notify', [
                'message' => 'Este ticket não pode ser avaliado no momento.',
                'type' => 'warning'
            ]);
            return;
        }

        $this->showRatingModal = true;
    }

    public function closeRatingModal()
    {
        $this->showRatingModal = false;
        $this->reset(['rating', 'ratingComment']);
        $this->resetValidation();
    }

    public function submitRating()
    {
        $this->validate([
            'rating' => $this->rules['rating'],
            'ratingComment' => $this->rules['ratingComment'],
        ]);

        try {
            $this->ticket->update([
                'rating' => $this->rating,
                'rating_comment' => $this->ratingComment,
                'status' => 'fechado',
                'closed_at' => now(),
            ]);

            $this->dispatch('notify', [
                'message' => 'Obrigado pela sua avaliação!',
                'type' => 'success'
            ]);

            $this->closeRatingModal();
            $this->ticket->refresh();

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao enviar avaliação. Tente novamente.',
                'type' => 'error'
            ]);
        }
    }

    public function render()
    {
        $messages = $this->ticket->publicMessages()
            ->with('user')
            ->orderBy('created_at', 'asc')
            ->get();

        return view('livewire.support.ticket-view', [
            'messages' => $messages
        ])->layout('layouts.app', ['title' => "Ticket #{$this->ticket->ticket_number}"]);
    }
}
