-- SQL para criar tabelas de hashtags e menções
-- Execute este SQL no phpMyAdmin ou via linha de comando

-- Criar tabela hashtags se não existir
CREATE TABLE IF NOT EXISTS hashtags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    posts_count INTEGER DEFAULT 0,
    created_at DATETIME,
    updated_at DATETIME
);

-- Criar índices para hashtags
CREATE INDEX IF NOT EXISTS idx_hashtags_name ON hashtags(name);
CREATE INDEX IF NOT EXISTS idx_hashtags_posts_count ON hashtags(posts_count);

-- Criar tabela post_hashtag se não existir
CREATE TABLE IF NOT EXISTS post_hashtag (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    post_id INTEGER NOT NULL,
    hashtag_id INTEGER NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    <PERSON><PERSON><PERSON><PERSON><PERSON> KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE,
    FOR<PERSON><PERSON><PERSON> KEY (hashtag_id) REFERENCES hashtags(id) ON DELETE CASCADE,
    UNIQUE(post_id, hashtag_id)
);

-- Criar índice para post_hashtag
CREATE INDEX IF NOT EXISTS idx_post_hashtag_hashtag_id ON post_hashtag(hashtag_id);

-- Criar tabela mentions se não existir
CREATE TABLE IF NOT EXISTS mentions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    mentioned_by INTEGER NOT NULL,
    mentionable_type VARCHAR(255) NOT NULL,
    mentionable_id INTEGER NOT NULL,
    position INTEGER NOT NULL,
    created_at DATETIME,
    updated_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (mentioned_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Inserir algumas hashtags de exemplo
INSERT OR IGNORE INTO hashtags (name, slug, posts_count, created_at, updated_at) VALUES
('swing', 'swing', 0, datetime('now'), datetime('now')),
('curitiba', 'curitiba', 0, datetime('now'), datetime('now')),
('festa', 'festa', 0, datetime('now'), datetime('now')),
('diversao', 'diversao', 0, datetime('now'), datetime('now')),
('noite', 'noite', 0, datetime('now'), datetime('now'));
