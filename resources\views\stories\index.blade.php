<x-layouts.app.sidebar :title="__('Stories')">
<div class="bg-gray-50 dark:bg-zinc-900">
    <div class="px-6 py-6">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Stories</h1>
                <p class="text-gray-600 dark:text-gray-400">Compartilhe momentos especiais que desaparecem em 24h</p>
            </div>
            
            <!-- Botõ<PERSON> de ação -->
            <div class="flex space-x-3">
                <livewire:stories.create-story />
                <a href="{{ route('reels.index') }}" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold rounded-lg transition-colors">
                    <flux:icon.video-camera class="w-5 h-5 mr-2" />
                    Ver <PERSON><PERSON>
                </a>
            </div>
        </div>

        <!-- Feed de Stories -->
        <div class="bg-white dark:bg-zinc-800 rounded-lg shadow-sm">
            <livewire:stories.stories-feed />
        </div>

        <!-- Informações sobre Stories -->
        <div class="mt-8 bg-white dark:bg-zinc-800 rounded-lg shadow-sm p-6">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Como funcionam os Stories?</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-3">
                        <flux:icon.camera class="w-6 h-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Crie e Compartilhe</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Adicione fotos, vídeos e texto aos seus stories. Use filtros para deixar ainda mais especial.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-pink-100 dark:bg-pink-900 rounded-full flex items-center justify-center mx-auto mb-3">
                        <flux:icon.clock class="w-6 h-6 text-pink-600 dark:text-pink-400" />
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Temporário</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Stories desaparecem automaticamente após 24 horas (ou o tempo que você definir).</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center mx-auto mb-3">
                        <flux:icon.star class="w-6 h-6 text-orange-600 dark:text-orange-400" />
                    </div>
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">Ganhe Pontos</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">Receba pontos por criar stories e por cada visualização que receber.</p>
                </div>
            </div>
        </div>
    </div>
</div>
</x-layouts.app.sidebar>

@push('scripts')
<script>
// Auto-refresh stories a cada 5 minutos
setInterval(function() {
    Livewire.dispatch('loadStories');
}, 300000);

// Notificações de recompensa
document.addEventListener('livewire:init', function() {
    Livewire.on('reward-earned', function(data) {
        // Criar animação de pontos
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 animate-bounce';
        notification.innerHTML = `+${data.points} pontos! 🎉`;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    });
});
</script>
@endpush
