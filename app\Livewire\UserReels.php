<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Post;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class UserReels extends Component
{
    public $user;
    public $reels = [];
    public $showModal = false;
    public $currentReelIndex = 0;
    public $isOwner = false;

    protected $listeners = [
        'reel-created' => 'loadReels',
        'show-user-reels' => 'showUserReels'
    ];

    public function mount($user = null)
    {
        if ($user) {
            $this->user = $user;
        } else {
            $this->user = Auth::user();
        }
        
        $this->isOwner = Auth::check() && Auth::id() === $this->user->id;
        $this->loadReels();
    }

    public function loadReels()
    {
        $this->reels = Post::reels()
            ->where('user_id', $this->user->id)
            ->with([
                'user',
                'user.userPhotos',
                'likedByUsers',
                'comments' => function($query) {
                    $query->latest()->limit(3);
                }
            ])
            ->latest()
            ->get()
            ->map(function ($reel) {
                return [
                    'id' => $reel->id,
                    'content' => $reel->content,
                    'video' => Storage::url($reel->video),
                    'thumbnail' => $this->generateThumbnail($reel->video),
                    'music_title' => $reel->music_title,
                    'music_url' => $reel->music_url,
                    'filters' => $reel->filters ?? [],
                    'duration' => $reel->duration,
                    'created_at' => $reel->created_at->diffForHumans(),
                    'likes_count' => $reel->likedByUsers->count(),
                    'comments_count' => $reel->comments->count(),
                    'is_liked' => Auth::check() ? $reel->isLikedBy(Auth::user()) : false,
                ];
            })
            ->toArray();
    }

    private function generateThumbnail($videoPath)
    {
        // Por enquanto, retorna uma imagem padrão
        // Em produção, você pode usar FFmpeg para gerar thumbnails reais
        return asset('images/video-thumbnail.svg');
    }

    public function showUserReels($userId = null)
    {
        if ($userId && $userId == $this->user->id) {
            $this->showModal = true;
        }
    }

    public function openReelViewer($index = 0)
    {
        $this->currentReelIndex = $index;
        $this->showModal = true;
    }

    public function closeReelViewer()
    {
        $this->showModal = false;
        $this->currentReelIndex = 0;
    }

    public function nextReel()
    {
        if ($this->currentReelIndex < count($this->reels) - 1) {
            $this->currentReelIndex++;
        }
    }

    public function previousReel()
    {
        if ($this->currentReelIndex > 0) {
            $this->currentReelIndex--;
        }
    }

    public function deleteReel($reelId)
    {
        if (!$this->isOwner) {
            $this->dispatch('notify', [
                'message' => 'Você não pode deletar este reel.',
                'type' => 'error'
            ]);
            return;
        }

        $reel = Post::find($reelId);
        
        if (!$reel || $reel->user_id !== Auth::id()) {
            $this->dispatch('notify', [
                'message' => 'Reel não encontrado.',
                'type' => 'error'
            ]);
            return;
        }

        try {
            // Deletar arquivo de vídeo
            if ($reel->video) {
                Storage::disk('public')->delete($reel->video);
            }

            $reel->delete();

            $this->dispatch('notify', [
                'message' => 'Reel deletado com sucesso!',
                'type' => 'success'
            ]);

            $this->loadReels();
            $this->closeReelViewer();

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'message' => 'Erro ao deletar reel.',
                'type' => 'error'
            ]);
        }
    }

    public function render()
    {
        return view('livewire.user-reels');
    }
}
