-- <PERSON><PERSON><PERSON><PERSON> chaves estrangeiras uma por vez
-- Execute cada comando separadamente para identificar problemas

-- 1. Primeira chave estrangeira
ALTER TABLE `support_tickets`
  ADD CONSTRAINT `support_tickets_user_id_foreign` 
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

-- 2. Segunda chave estrangeira (execute apenas se a primeira funcionou)
-- ALTER TABLE `support_tickets`
--   ADD CONSTRAINT `support_tickets_assigned_to_foreign` 
--   FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL;

-- 3. Terc<PERSON> chave estrangeira (execute apenas se as anteriores funcionaram)
-- ALTER TABLE `support_ticket_messages`
--   ADD CONSTRAINT `support_ticket_messages_ticket_id_foreign` 
--   FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE;

-- 4. <PERSON><PERSON><PERSON> chave estrangeira (execute apenas se as anteriores funcionaram)
-- ALTER TABLE `support_ticket_messages`
--   ADD CONSTRAINT `support_ticket_messages_user_id_foreign` 
--   FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
