<x-layouts.app.sidebar :title="__('Teste de Ícones')">
<div class="bg-gray-50 dark:bg-zinc-900">
    <div class="px-6 py-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">Teste de Ícones Corrigidos</h1>
        
        <!-- Teste da barra de stories -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Barra de Stories</h2>
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-4">
                @try
                    <livewire:stories.stories-bar />
                @catch(\Exception $e)
                    <div class="text-red-500">Erro na barra de stories: {{ $e->getMessage() }}</div>
                @endtry
            </div>
        </div>

        <!-- Teste dos botões de criação -->
        <div class="mb-8">
            <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Bo<PERSON>ões de Criação</h2>
            <div class="bg-white dark:bg-zinc-800 rounded-lg p-4">
                <div class="flex space-x-4">
                    @try
                        <livewire:stories.create-story />
                    @catch(\Exception $e)
                        <div class="text-red-500">Erro no botão de story: {{ $e->getMessage() }}</div>
                    @endtry

                    @try
                        <livewire:stories.create-reel />
                    @catch(\Exception $e)
                        <div class="text-red-500">Erro no botão de reel: {{ $e->getMessage() }}</div>
                    @endtry
                </div>
            </div>
        </div>
        
        <!-- Status -->
        <div class="bg-green-100 dark:bg-green-900 rounded-lg p-6">
            <h2 class="text-xl font-semibold text-green-800 dark:text-green-200 mb-4">✅ Status dos Ícones</h2>
            <p class="text-green-700 dark:text-green-300">
                Todos os ícones do Flux UI foram convertidos para SVGs nativos. 
                Se esta página carregar sem erros, o problema foi resolvido!
            </p>
            
            <div class="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <p class="text-sm">Camera</p>
                </div>
                
                <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <p class="text-sm">Video</p>
                </div>
                
                <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                    <p class="text-sm">Heart</p>
                </div>
                
                <div class="text-center">
                    <svg class="w-8 h-8 mx-auto mb-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path>
                    </svg>
                    <p class="text-sm">Music</p>
                </div>
            </div>
        </div>
        
        <!-- Links de navegação -->
        <div class="mt-8 flex space-x-4">
            <a href="{{ route('dashboard') }}" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
                ← Voltar ao Dashboard
            </a>
            <a href="{{ route('stories.index') }}" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg">
                Ver Stories
            </a>
            <a href="{{ route('reels.index') }}" class="bg-indigo-500 hover:bg-indigo-600 text-white px-6 py-3 rounded-lg">
                Ver Reels
            </a>
        </div>
    </div>
</div>
</x-layouts.app.sidebar>
