<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        if (!Schema::hasTable('posts')) {
            Schema::create('posts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->foreignId('group_id')->nullable()->constrained()->onDelete('cascade');
                $table->unsignedInteger('likes_count')->default(0);
                $table->text('content');
                $table->string('image')->nullable();
                $table->string('video')->nullable();

                // Campos para Stories/Reels
                $table->enum('type', ['post', 'story', 'reel'])->default('post');
                $table->timestamp('expires_at')->nullable(); // Para stories temporários
                $table->json('story_views')->nullable(); // IDs dos usuários que visualizaram
                $table->boolean('is_highlight')->default(false); // Stories em destaque
                $table->string('music_url')->nullable(); // URL da música para reels
                $table->string('music_title')->nullable(); // Título da música
                $table->json('filters')->nullable(); // Filtros aplicados
                $table->integer('duration')->nullable(); // Duração em segundos

                $table->timestamps();

                // Índices para performance
                $table->index(['type', 'created_at']);
                $table->index(['user_id', 'type']);
                $table->index(['expires_at']);
            });
        }
    }

    public function down(): void {
        Schema::dropIfExists('posts');
    }
};
