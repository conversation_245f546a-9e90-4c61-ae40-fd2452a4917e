<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('story_views', function (Blueprint $table) {
            $table->id();
            $table->foreignId('post_id')->constrained()->onDelete('cascade'); // Story/Reel
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // Quem visualizou
            $table->timestamp('viewed_at')->useCurrent();
            $table->timestamps();
            
            // Evitar visualizações duplicadas
            $table->unique(['post_id', 'user_id']);
            
            // Índices para performance
            $table->index(['post_id', 'viewed_at']);
            $table->index(['user_id', 'viewed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('story_views');
    }
};
