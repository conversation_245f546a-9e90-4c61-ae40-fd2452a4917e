-- Adicionar campos premium aos tickets de suporte
-- Execute este SQL no phpMyAdmin do KingHost

-- Verificar se os campos já existem antes de adicionar
SET @sql = '';

-- Verificar se a coluna is_premium existe
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'support_tickets' 
  AND COLUMN_NAME = 'is_premium';

-- Se não existir, criar a coluna
IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE support_tickets ADD COLUMN is_premium TINYINT(1) NOT NULL DEFAULT 0;');
END IF;

-- Verificar se a coluna premium_amount existe
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'support_tickets' 
  AND COLUMN_NAME = 'premium_amount';

-- Se não existir, criar a coluna
IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE support_tickets ADD COLUMN premium_amount DECIMAL(8,2) DEFAULT NULL;');
END IF;

-- Executar as alterações se necessário
IF LENGTH(@sql) > 0 THEN
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'Campos premium adicionados com sucesso!' as resultado;
ELSE
    SELECT 'Campos premium já existem na tabela.' as resultado;
END IF;

-- Verificar se o campo wallet_balance existe na tabela users
SELECT COUNT(*) INTO @wallet_exists 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'users' 
  AND COLUMN_NAME = 'wallet_balance';

-- Se não existir, criar o campo wallet_balance
IF @wallet_exists = 0 THEN
    ALTER TABLE users ADD COLUMN wallet_balance DECIMAL(10,2) NOT NULL DEFAULT 0.00;
    SELECT 'Campo wallet_balance adicionado à tabela users!' as resultado_wallet;
ELSE
    SELECT 'Campo wallet_balance já existe na tabela users.' as resultado_wallet;
END IF;
