-- Parte 4: <PERSON><PERSON><PERSON><PERSON> chaves estrangeiras
-- Execute este arquivo após criar todas as tabelas

-- <PERSON><PERSON>, vamos verificar e ajustar os tipos de dados se necessário

-- Verificar se a tabela users existe e qual o tipo da coluna id
-- Se necessário, ajustar o tipo das colunas user_id para corresponder

-- Verificar estrutura da tabela users
-- DESCRIBE users;

-- Se a coluna id da tabela users for int(11), execute estas linhas:
-- ALTER TABLE `support_tickets` MODIFY `user_id` int(11) UNSIGNED NOT NULL;
-- ALTER TABLE `support_tickets` MODIFY `assigned_to` int(11) UNSIGNED DEFAULT NULL;
-- ALTER TABLE `support_ticket_messages` MODIFY `user_id` int(11) UNSIGNED NOT NULL;

-- Se a coluna id da tabela users for bigint(20), as tabelas já estão corretas

-- Ad<PERSON><PERSON>r chaves estrangeiras para support_tickets (uma por vez para melhor debug)
ALTER TABLE `support_tickets`
  ADD CONSTRAINT `support_tickets_user_id_foreign`
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
