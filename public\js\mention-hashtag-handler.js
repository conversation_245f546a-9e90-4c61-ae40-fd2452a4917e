/**
 * Sistema de autocomplete para menções (@) e hashtags (#)
 */

class MentionHashtagHandler {
    constructor() {
        console.log('MentionHashtagHandler construído');
        this.mentionSuggestions = [];
        this.hashtagSuggestions = [];
        this.currentMentionStartPosition = null;
        this.currentHashtagStartPosition = null;
        this.mentionQuery = '';
        this.hashtagQuery = '';
        this.showMentionSuggestions = false;
        this.showHashtagSuggestions = false;
        this.suggestionTimeout = null;
    }

    init(textarea) {
        console.log('Inicializando textarea:', textarea);
        this.textarea = textarea;
        this.setupEventListeners();
    }

    setupEventListeners() {
        console.log('Configurando event listeners');
        this.textarea.addEventListener('input', this.handleInput.bind(this));
        this.textarea.addEventListener('keydown', this.handleKeydown.bind(this));
        document.addEventListener('click', this.handleClickOutside.bind(this));
    }

    handleInput(event) {
        const content = event.target.value;
        const cursorPosition = event.target.selectionStart;
        console.log('Input detectado:', { content, cursorPosition });

        // Verificar menções
        if (content[cursorPosition - 1] === '@') {
            console.log('@ detectado');
            this.currentMentionStartPosition = cursorPosition - 1;
            this.mentionQuery = '';
            this.showMentionSuggestions = true;
            this.fetchMentionSuggestions('');
        } else if (this.currentMentionStartPosition !== null) {
            const textAfterAt = content.substring(this.currentMentionStartPosition + 1, cursorPosition);
            if (textAfterAt.includes(' ')) {
                this.resetMentionState();
            } else {
                this.mentionQuery = textAfterAt;
                this.fetchMentionSuggestions(textAfterAt);
            }
        }

        // Verificar hashtags
        if (content[cursorPosition - 1] === '#') {
            console.log('# detectado');
            this.currentHashtagStartPosition = cursorPosition - 1;
            this.hashtagQuery = '';
            this.showHashtagSuggestions = true;
            this.fetchHashtagSuggestions('');
        } else if (this.currentHashtagStartPosition !== null) {
            const textAfterHash = content.substring(this.currentHashtagStartPosition + 1, cursorPosition);
            if (textAfterHash.includes(' ')) {
                this.resetHashtagState();
            } else {
                this.hashtagQuery = textAfterHash;
                this.fetchHashtagSuggestions(textAfterHash);
            }
        }
    }

    handleKeydown(event) {
        if (event.key === 'Escape') {
            this.resetMentionState();
            this.resetHashtagState();
        }
    }

    handleClickOutside(event) {
        if (!event.target.closest('.mention-suggestions') && !event.target.closest('.hashtag-suggestions')) {
            this.resetMentionState();
            this.resetHashtagState();
        }
    }

    async fetchMentionSuggestions(query) {
        if (this.suggestionTimeout) {
            clearTimeout(this.suggestionTimeout);
        }

        this.suggestionTimeout = setTimeout(async () => {
            try {
                const response = await fetch(`/api/users/search?query=${encodeURIComponent(query)}&limit=5`);
                if (response.ok) {
                    this.mentionSuggestions = await response.json();
                    this.showMentionSuggestions = this.mentionSuggestions.length > 0;
                    this.renderMentionSuggestions();
                }
            } catch (error) {
                console.error('Erro ao buscar sugestões de menções:', error);
            }
        }, 300);
    }

    async fetchHashtagSuggestions(query) {
        if (this.suggestionTimeout) {
            clearTimeout(this.suggestionTimeout);
        }

        this.suggestionTimeout = setTimeout(async () => {
            try {
                const response = await fetch(`/api/hashtags/search?query=${encodeURIComponent(query)}&limit=5`);
                if (response.ok) {
                    this.hashtagSuggestions = await response.json();
                    this.showHashtagSuggestions = this.hashtagSuggestions.length > 0;
                    this.renderHashtagSuggestions();
                }
            } catch (error) {
                console.error('Erro ao buscar sugestões de hashtags:', error);
            }
        }, 300);
    }

    renderMentionSuggestions() {
        let container = document.querySelector('.mention-suggestions');
        if (!container) {
            container = document.createElement('div');
            container.className = 'mention-suggestions absolute z-50 bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto';
            this.textarea.parentNode.appendChild(container);
        }

        if (this.showMentionSuggestions) {
            container.innerHTML = this.mentionSuggestions.map(user => `
                <div class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300 flex items-center"
                     onclick="window.mentionHandler.selectMention('${user.username}')">
                    <img src="${user.avatar}" alt="${user.username}" class="w-6 h-6 rounded-full mr-2">
                    <span>${user.name} (@${user.username})</span>
                </div>
            `).join('');
            container.style.display = 'block';
        } else {
            container.style.display = 'none';
        }
    }

    renderHashtagSuggestions() {
        let container = document.querySelector('.hashtag-suggestions');
        if (!container) {
            container = document.createElement('div');
            container.className = 'hashtag-suggestions absolute z-50 bg-neutral-800 border border-neutral-700 rounded-md shadow-lg max-h-48 overflow-auto';
            this.textarea.parentNode.appendChild(container);
        }

        if (this.showHashtagSuggestions) {
            container.innerHTML = this.hashtagSuggestions.map(hashtag => `
                <div class="px-4 py-2 hover:bg-neutral-700 cursor-pointer text-gray-300"
                     onclick="window.mentionHandler.selectHashtag('${hashtag.name}')">
                    #${hashtag.name}
                </div>
            `).join('');
            container.style.display = 'block';
        } else {
            container.style.display = 'none';
        }
    }

    selectMention(username) {
        if (this.currentMentionStartPosition !== null) {
            const textBeforeMention = this.textarea.value.substring(0, this.currentMentionStartPosition);
            const textAfterMention = this.textarea.value.substring(this.currentMentionStartPosition + this.mentionQuery.length + 1);
            this.textarea.value = textBeforeMention + '@' + username + ' ' + textAfterMention;
            this.resetMentionState();
        }
    }

    selectHashtag(name) {
        if (this.currentHashtagStartPosition !== null) {
            const textBeforeHashtag = this.textarea.value.substring(0, this.currentHashtagStartPosition);
            const textAfterHashtag = this.textarea.value.substring(this.currentHashtagStartPosition + this.hashtagQuery.length + 1);
            this.textarea.value = textBeforeHashtag + '#' + name + ' ' + textAfterHashtag;
            this.resetHashtagState();
        }
    }

    resetMentionState() {
        this.currentMentionStartPosition = null;
        this.mentionQuery = '';
        this.showMentionSuggestions = false;
        this.mentionSuggestions = [];
        const container = document.querySelector('.mention-suggestions');
        if (container) {
            container.style.display = 'none';
        }
    }

    resetHashtagState() {
        this.currentHashtagStartPosition = null;
        this.hashtagQuery = '';
        this.showHashtagSuggestions = false;
        this.hashtagSuggestions = [];
        const container = document.querySelector('.hashtag-suggestions');
        if (container) {
            container.style.display = 'none';
        }
    }
}

// Exportar para uso global
window.mentionHandler = new MentionHashtagHandler();

// Inicializar para todos os campos com a classe 'mention-hashtag-input'
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM carregado, procurando campos de texto...');
    const textInputs = document.querySelectorAll('.mention-hashtag-input');
    console.log('Campos de texto encontrados:', textInputs.length);
    textInputs.forEach(input => {
        window.mentionHandler.init(input);
    });
});

// Observar mudanças no DOM para inicializar novos campos
const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
        if (mutation.addedNodes.length) {
            mutation.addedNodes.forEach((node) => {
                if (node.nodeType === 1) { // Element node
                    const inputs = node.querySelectorAll('.mention-hashtag-input');
                    inputs.forEach(input => {
                        window.mentionHandler.init(input);
                    });
                }
            });
        }
    });
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

document.addEventListener('alpine:init', () => {
    Alpine.data('mentionHashtagHandler', () => ({
        showMentions: false,
        showHashtags: false,
        mentionSuggestions: [],
        hashtagSuggestions: [],
        mentionQuery: '',
        hashtagQuery: '',
        mentionStyle: {},
        hashtagStyle: {},
        currentMentionStart: null,
        currentHashtagStart: null,
        suggestionTimeout: null,

        init() {
            console.log('Componente inicializado');
        },

        handleInput(event) {
            console.log('Input detectado');
            const textarea = event.target;
            const content = textarea.value;
            const cursorPosition = textarea.selectionStart;
            const lastChar = content[cursorPosition - 1];

            if (lastChar === '@') {
                this.currentMentionStart = cursorPosition - 1;
                this.mentionQuery = '';
                this.showMentions = true;
                this.updateMentionPosition(textarea);
                this.fetchMentionSuggestions('');
            } else if (this.currentMentionStart !== null) {
                const textAfterAt = content.substring(this.currentMentionStart + 1, cursorPosition);
                if (textAfterAt.includes(' ')) {
                    this.resetMentionState();
                } else {
                    this.mentionQuery = textAfterAt;
                    this.updateMentionPosition(textarea);
                    this.fetchMentionSuggestions(textAfterAt);
                }
            }

            if (lastChar === '#') {
                this.currentHashtagStart = cursorPosition - 1;
                this.hashtagQuery = '';
                this.showHashtags = true;
                this.updateHashtagPosition(textarea);
                this.fetchHashtagSuggestions('');
            } else if (this.currentHashtagStart !== null) {
                const textAfterHash = content.substring(this.currentHashtagStart + 1, cursorPosition);
                if (textAfterHash.includes(' ')) {
                    this.resetHashtagState();
                } else {
                    this.hashtagQuery = textAfterHash;
                    this.updateHashtagPosition(textarea);
                    this.fetchHashtagSuggestions(textAfterHash);
                }
            }
        },

        handleKeydown(event) {
            console.log('Keydown detectado');
            if (event.key === 'Escape') {
                this.resetMentionState();
                this.resetHashtagState();
            }
        },

        updateMentionPosition(textarea) {
            const textareaRect = textarea.getBoundingClientRect();
            const textBeforeCursor = textarea.value.substring(0, textarea.selectionStart);
            const lines = textBeforeCursor.split('\n');
            const currentLine = lines[lines.length - 1];

            const temp = document.createElement('span');
            temp.style.visibility = 'hidden';
            temp.style.position = 'absolute';
            temp.style.whiteSpace = 'pre';
            temp.style.font = window.getComputedStyle(textarea).font;
            temp.textContent = currentLine;
            document.body.appendChild(temp);

            const textWidth = temp.offsetWidth;
            document.body.removeChild(temp);

            const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight);
            const cursorTop = textareaRect.top + (lines.length - 1) * lineHeight;
            const cursorLeft = textareaRect.left + textWidth;

            this.mentionStyle = {
                position: 'fixed',
                top: `${cursorTop - 130}px`,
                left: `${cursorLeft}px`,
                zIndex: '9999'
            };
        },

        updateHashtagPosition(textarea) {
            const textareaRect = textarea.getBoundingClientRect();
            const textBeforeCursor = textarea.value.substring(0, textarea.selectionStart);
            const lines = textBeforeCursor.split('\n');
            const currentLine = lines[lines.length - 1];

            const temp = document.createElement('span');
            temp.style.visibility = 'hidden';
            temp.style.position = 'absolute';
            temp.style.whiteSpace = 'pre';
            temp.style.font = window.getComputedStyle(textarea).font;
            temp.textContent = currentLine;
            document.body.appendChild(temp);

            const textWidth = temp.offsetWidth;
            document.body.removeChild(temp);

            const lineHeight = parseInt(window.getComputedStyle(textarea).lineHeight);
            const cursorTop = textareaRect.top + (lines.length - 1) * lineHeight;
            const cursorLeft = textareaRect.left + textWidth;

            this.hashtagStyle = {
                position: 'fixed',
                top: `${cursorTop - 130}px`,
                left: `${cursorLeft}px`,
                zIndex: '9999'
            };
        },

        async fetchMentionSuggestions(query) {
            if (this.suggestionTimeout) {
                clearTimeout(this.suggestionTimeout);
            }

            this.suggestionTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`/api/users/search?query=${encodeURIComponent(query)}&limit=5`);
                    if (response.ok) {
                        this.mentionSuggestions = await response.json();
                        this.showMentions = this.mentionSuggestions.length > 0;
                    }
                } catch (error) {
                    console.error('Erro ao buscar sugestões de menções:', error);
                }
            }, 300);
        },

        async fetchHashtagSuggestions(query) {
            if (this.suggestionTimeout) {
                clearTimeout(this.suggestionTimeout);
            }

            this.suggestionTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`/api/hashtags/search?query=${encodeURIComponent(query)}&limit=5`);
                    if (response.ok) {
                        this.hashtagSuggestions = await response.json();
                        this.showHashtags = this.hashtagSuggestions.length > 0;
                    }
                } catch (error) {
                    console.error('Erro ao buscar sugestões de hashtags:', error);
                }
            }, 300);
        },

        selectMention(username) {
            const textarea = this.$refs.textarea;
            const content = textarea.value;
            const beforeMention = content.substring(0, this.currentMentionStart);
            const afterMention = content.substring(textarea.selectionStart);
            textarea.value = `${beforeMention}@${username} ${afterMention}`;
            this.resetMentionState();
        },

        selectHashtag(name) {
            const textarea = this.$refs.textarea;
            const content = textarea.value;
            const beforeHashtag = content.substring(0, this.currentHashtagStart);
            const afterHashtag = content.substring(textarea.selectionStart);
            textarea.value = `${beforeHashtag}#${name} ${afterHashtag}`;
            this.resetHashtagState();
        },

        resetMentionState() {
            this.showMentions = false;
            this.mentionSuggestions = [];
            this.mentionQuery = '';
            this.currentMentionStart = null;
        },

        resetHashtagState() {
            this.showHashtags = false;
            this.hashtagSuggestions = [];
            this.hashtagQuery = '';
            this.currentHashtagStart = null;
        }
    }));
});
