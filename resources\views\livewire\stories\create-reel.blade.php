<div>
    <!-- <PERSON><PERSON><PERSON> para abrir modal -->
    <button
        wire:click="openModal"
        class="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold px-6 py-3 rounded-full shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center"
    >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
        Criar <PERSON>el
    </button>

    <!-- Modal para criar reel -->
    <div x-data="{ showModal: @entangle('showModal') }" x-show="showModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="showModal = false"></div>
        <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block w-full max-w-3xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-zinc-800 shadow-xl rounded-lg">
        <div class="mb-6">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-900 dark:text-white">Criar Novo Reel</h2>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Crie um vídeo curto e envolvente</p>
                </div>
            </div>
        </div>

        <form wire:submit="createReel">
            <div class="space-y-6">
                <!-- Upload de vídeo (obrigatório) -->
                <div>
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Vídeo *</label>
                        <input type="file" wire:model="video" accept="video/*" required class="w-full file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 dark:file:bg-blue-900 dark:file:text-blue-300">
                        @error('video') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        <p class="text-xs text-gray-500 mt-1">Máximo 100MB. Formatos: MP4, MOV, AVI, WMV</p>
                    </div>
                    
                    @if ($video)
                        <div class="mt-4">
                            <video controls class="w-full max-h-64 object-cover rounded-lg">
                                <source src="{{ $video->temporaryUrl() }}" type="video/mp4">
                            </video>
                        </div>
                    @endif
                </div>

                <!-- Conteúdo do reel -->
                <div>
                    <div class="space-y-2">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Descrição</label>
                        <textarea wire:model="content" placeholder="Descreva seu reel... Use #hashtags e @menções" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white resize-none"></textarea>
                        @error('content') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        <p class="text-xs text-gray-500 mt-1">Máximo 2000 caracteres</p>
                    </div>
                </div>

                <!-- Música -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Música</label>
                    
                    <!-- Músicas populares -->
                    <div class="mt-2">
                        <p class="text-sm text-gray-600 mb-2">Músicas em alta:</p>
                        <div class="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                            @foreach($popularMusic as $music)
                                <button 
                                    type="button"
                                    wire:click="selectMusic('{{ $music['url'] }}', '{{ $music['title'] }}')"
                                    class="flex items-center p-2 text-left rounded-lg border hover:bg-gray-50 transition-colors
                                        {{ $music_title === $music['title'] ? 'border-blue-300 bg-blue-50' : 'border-gray-200' }}"
                                >
                                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>
                                    <span class="text-sm">{{ $music['title'] }}</span>
                                </button>
                            @endforeach
                        </div>
                    </div>

                    <!-- Música personalizada -->
                    <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Nome da música</label>
                            <input
                                type="text"
                                wire:model="music_title"
                                placeholder="Nome da música"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                            />
                        </div>

                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">URL da música</label>
                            <input
                                type="url"
                                wire:model="music_url"
                                placeholder="https://..."
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-zinc-700 dark:text-white"
                            />
                            @error('music_url') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                        </div>
                    </div>
                </div>

                <!-- Filtros disponíveis -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Filtros de vídeo</label>
                    <div class="flex flex-wrap gap-2 mt-2">
                        @foreach(['cinematic', 'vintage', 'vibrant', 'dramatic', 'soft', 'sharp', 'warm', 'cool'] as $filter)
                            <button
                                type="button"
                                wire:click="addFilter('{{ $filter }}')"
                                class="px-3 py-1 text-xs rounded-full border transition-colors
                                    {{ in_array($filter, $filters)
                                        ? 'bg-blue-100 border-blue-300 text-blue-700'
                                        : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
                                    }}"
                            >
                                {{ ucfirst($filter) }}
                            </button>
                        @endforeach
                    </div>

                    @if(count($filters) > 0)
                        <div class="mt-2">
                            <p class="text-xs text-gray-600">Filtros selecionados:</p>
                            <div class="flex flex-wrap gap-1 mt-1">
                                @foreach($filters as $filter)
                                    <span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded-full">
                                        {{ ucfirst($filter) }}
                                        <button
                                            type="button"
                                            wire:click="removeFilter('{{ $filter }}')"
                                            class="ml-1 text-blue-500 hover:text-blue-700"
                                        >
                                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                                        </button>
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Loading indicator -->
                <div wire:loading wire:target="createReel" class="flex items-center justify-center py-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span class="ml-2 text-gray-600">Criando reel...</span>
                </div>
            </div>

            <div class="mt-6 flex justify-between">
                <div class="flex justify-between w-full">
                    <button type="button" wire:click="closeModal" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-zinc-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-zinc-600 focus:outline-none focus:ring-2 focus:ring-purple-500">
                        Cancelar
                    </button>

                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50" wire:loading.attr="disabled">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path></svg>
                        Publicar Reel
                    </button>
                </div>
            </div>
        </form>
        </div>
    </div>
</div>
</div>
