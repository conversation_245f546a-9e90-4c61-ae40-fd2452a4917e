-- SQL para implementar sistema de desativação de usuários
-- Execute este SQL no phpMyAdmin para configurar o sistema

-- 1. Verificar se a coluna 'active' existe na tabela users
SELECT COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'users' 
AND COLUMN_NAME = 'active' 
AND TABLE_SCHEMA = DATABASE();

-- 2. Se a coluna não existir, adicione-a (descomente a linha abaixo)
-- ALTER TABLE users ADD COLUMN active BOOLEAN DEFAULT TRUE AFTER role;

-- 3. <PERSON><PERSON><PERSON><PERSON> que todos os usuários existentes sejam marcados como ativos
UPDATE users SET active = 1 WHERE active IS NULL;

-- 4. Verificar usuários ativos e inativos
SELECT 
    active,
    COUNT(*) as total_usuarios,
    CASE 
        WHEN active = 1 THEN 'Ativos'
        WHEN active = 0 THEN 'Inativos'
        ELSE 'Indefinido'
    END as status_descricao
FROM users 
GROUP BY active;

-- 5. Verificar usuários por papel e status
SELECT 
    role,
    active,
    COUNT(*) as total
FROM users 
GROUP BY role, active
ORDER BY role, active;

-- 6. Listar usuários inativos (se houver)
SELECT 
    id,
    name,
    email,
    role,
    active,
    created_at,
    updated_at
FROM users 
WHERE active = 0
ORDER BY updated_at DESC;

-- 7. Verificar integridade dos dados relacionados
-- Posts de usuários inativos
SELECT 
    u.id as user_id,
    u.name,
    u.active,
    COUNT(p.id) as total_posts
FROM users u
LEFT JOIN posts p ON u.id = p.user_id
WHERE u.active = 0
GROUP BY u.id, u.name, u.active;

-- 8. Mensagens de usuários inativos
SELECT 
    u.id as user_id,
    u.name,
    u.active,
    COUNT(m.id) as total_messages_sent
FROM users u
LEFT JOIN messages m ON u.id = m.sender_id
WHERE u.active = 0
GROUP BY u.id, u.name, u.active;

-- 9. Transações de carteira de usuários inativos
SELECT 
    u.id as user_id,
    u.name,
    u.active,
    COUNT(wt.id) as total_transactions,
    COALESCE(SUM(wt.amount), 0) as total_amount
FROM users u
LEFT JOIN wallet_transactions wt ON u.id = wt.user_id
WHERE u.active = 0
GROUP BY u.id, u.name, u.active;

-- 10. Função para desativar usuário (exemplo de uso)
-- UPDATE users 
-- SET active = 0, 
--     email = CONCAT(email, '_deactivated_', UNIX_TIMESTAMP())
-- WHERE id = [USER_ID];

-- 11. Função para reativar usuário (exemplo de uso)
-- UPDATE users 
-- SET active = 1,
--     email = REGEXP_REPLACE(email, '_deactivated_[0-9]+$', '')
-- WHERE id = [USER_ID];

-- 12. Verificar emails duplicados que podem causar problemas na reativação
SELECT 
    REGEXP_REPLACE(email, '_deactivated_[0-9]+$', '') as original_email,
    COUNT(*) as total_occurrences
FROM users 
WHERE email LIKE '%_deactivated_%'
GROUP BY REGEXP_REPLACE(email, '_deactivated_[0-9]+$', '')
HAVING COUNT(*) > 1;

-- 13. Criar índice para melhor performance nas consultas por status
-- CREATE INDEX idx_users_active ON users(active);

-- 14. Estatísticas finais
SELECT 
    'Total de usuários' as metric,
    COUNT(*) as value
FROM users
UNION ALL
SELECT 
    'Usuários ativos' as metric,
    COUNT(*) as value
FROM users WHERE active = 1
UNION ALL
SELECT 
    'Usuários inativos' as metric,
    COUNT(*) as value
FROM users WHERE active = 0;
